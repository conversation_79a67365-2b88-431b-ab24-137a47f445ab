<?php
/**
 * Test the exact query used in sessions.php to see if it works
 */

// WordPress environment setup
require_once('C:/xampp/htdocs/paylearn/wp-config.php');
require_once('database/helper.php');

echo "=== Testing Sessions Query ===\n\n";

// Test the exact method used in sessions.php
echo "Testing VedMG_ClassRoom_Database_Helper::get_class_sessions_with_auto_update()...\n";

try {
    $session_data = VedMG_ClassRoom_Database_Helper::get_class_sessions_with_auto_update(1, 10, '', '');
    
    echo "✅ Query executed successfully\n";
    echo "Total count: " . $session_data['total_count'] . "\n";
    echo "Total pages: " . $session_data['total_pages'] . "\n";
    echo "Sessions returned: " . count($session_data['sessions']) . "\n\n";
    
    if (!empty($session_data['sessions'])) {
        echo "=== Sample Session Data ===\n";
        $first_session = $session_data['sessions'][0];
        foreach ($first_session as $key => $value) {
            echo "$key: " . (is_null($value) ? 'NULL' : $value) . "\n";
        }
    } else {
        echo "❌ No sessions returned despite having data in database\n";
        
        // Let's check what the raw query returns
        global $wpdb;
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        echo "\n=== Debugging Raw Query ===\n";
        
        // Test if courses table exists
        $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
        if (!$courses_exists) {
            echo "❌ Courses table does not exist: $courses_table\n";
        } else {
            echo "✅ Courses table exists: $courses_table\n";
            
            $courses_count = $wpdb->get_var("SELECT COUNT(*) FROM $courses_table");
            echo "Courses count: $courses_count\n";
        }
        
        // Test the JOIN query manually
        $test_query = "
            SELECT COUNT(*)
            FROM $sessions_table s
            LEFT JOIN $courses_table c ON s.course_id = c.course_id
            LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
            LEFT JOIN {$wpdb->users} assigned_u ON s.assigned_instructor_id = assigned_u.ID
        ";
        
        $join_count = $wpdb->get_var($test_query);
        echo "JOIN query count: $join_count\n";
        
        if ($wpdb->last_error) {
            echo "❌ SQL Error: " . $wpdb->last_error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

// Test classroom options
echo "\n=== Testing Classroom Options ===\n";
try {
    $classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();
    echo "Classroom options count: " . count($classroom_options) . "\n";
    
    if (!empty($classroom_options)) {
        echo "Sample classroom option:\n";
        $first_option = $classroom_options[0];
        foreach ($first_option as $key => $value) {
            echo "  $key: " . (is_null($value) ? 'NULL' : $value) . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Exception getting classroom options: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
?>
