<?php
/**
 * VedMG ClassRoom Database Helper
 * 
 * This file provides helper functions to fetch data from the database tables
 * for display in the admin panel. It bridges the gap between database and UI.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom Database Helper Class
 * 
 * Provides functions to fetch and format data from database tables
 */
class VedMG_ClassRoom_Database_Helper {
    
    /**
     * Get courses from database with pagination support
     * 
     * @param int $page Current page number (1-based)
     * @param int $per_page Number of items per page
     * @param string $search_filter Optional search filter
     * @return array Array containing courses, total_count, and total_pages
     */
    public static function get_courses($page = 1, $per_page = 10, $search_filter = '') {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if table exists first
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Courses table does not exist yet');
                return array(
                    'courses' => array(),
                    'total_count' => 0,
                    'total_pages' => 0
                );
            }

            // Sanitize pagination parameters
            $page = max(1, intval($page));
            $per_page = max(1, min(100, intval($per_page)));
            
            // Build WHERE clause for search
            $where_clause = '';
            $where_values = array();
            
            if (!empty($search_filter)) {
                $search_filter = '%' . $wpdb->esc_like($search_filter) . '%';
                $where_clause = "WHERE (c.course_name LIKE %s OR c.course_description LIKE %s OR c.instructor_name LIKE %s OR u.display_name LIKE %s)";
                $where_values = array($search_filter, $search_filter, $search_filter, $search_filter);
            }

            // Get total count for pagination
            $count_sql = "
                SELECT COUNT(*)
                FROM $courses_table c
                LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
                $where_clause
            ";
            
            if (!empty($where_values)) {
                $count_sql = $wpdb->prepare($count_sql, $where_values);
            }
            
            $total_count = intval($wpdb->get_var($count_sql));
            $total_pages = ceil($total_count / $per_page);

            // Calculate offset for pagination
            $offset = ($page - 1) * $per_page;
            
            // Get courses with instructor information and pagination
            $sql = "
                SELECT
                    c.course_id,
                    c.course_name,
                    c.course_description,
                    c.instructor_id,
                    c.google_classroom_id,
                    c.meeting_link,
                    c.classroom_status,
                    c.auto_enroll_enabled,
                    c.created_date,
                    c.updated_date,
                    COALESCE(c.instructor_name, u.display_name, 'Unknown') as instructor_name,
                    COALESCE(c.instructor_email, u.user_email) as instructor_email,
                    (SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_enrollments e WHERE e.course_id = c.course_id) as student_count
                FROM $courses_table c
                LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
                $where_clause
                ORDER BY c.created_date DESC
                LIMIT %d OFFSET %d
            ";
            
            // Prepare the final query with pagination
            $pagination_values = array_merge($where_values, array($per_page, $offset));
            $sql = $wpdb->prepare($sql, $pagination_values);
            
            $courses = $wpdb->get_results($sql);
            
            vedmg_log_database('SELECT', 'vedmg_courses', "Retrieved " . count($courses) . " courses (page $page of $total_pages)");
            
            return array(
                'courses' => $courses,
                'total_count' => $total_count,
                'total_pages' => $total_pages
            );
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching courses', $e->getMessage());
            return array(
                'courses' => array(),
                'total_count' => 0,
                'total_pages' => 0
            );
        }
    }
    
    /**
     * Get all student enrollments from database with pagination support
     * Fetches enrollments from vedmg_student_enrollments table with student and course info
     *
     * @param int $page Current page number (1-based)
     * @param int $per_page Number of items per page
     * @param string $course_filter Filter by course ID
     * @param string $status_filter Filter by enrollment status
     * @param string $classroom_filter Filter by classroom ID
     * @param string $search_query Search query for student name, email, instructor, course name, or classroom ID
     * @return array Array containing 'enrollments', 'total_count', 'page', 'per_page', 'total_pages'
     */
    public static function get_student_enrollments($page = 1, $per_page = 10, $course_filter = '', $status_filter = '', $classroom_filter = '', $search_query = '') {
        global $wpdb;
        
        $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist first
            $enrollments_exists = $wpdb->get_var("SHOW TABLES LIKE '$enrollments_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$enrollments_exists || !$courses_exists) {
                vedmg_log_warning('DATABASE', 'Required tables do not exist yet');
                return array(
                    'enrollments' => array(),
                    'total_count' => 0,
                    'page' => $page,
                    'per_page' => $per_page,
                    'total_pages' => 0
                );
            }
            
            // Build WHERE conditions for filtering
            $where_conditions = array('1=1');
            $where_values = array();
            
            if (!empty($course_filter)) {
                $where_conditions[] = 'e.course_id = %d';
                $where_values[] = intval($course_filter);
            }
            
            // Note: enrollment_status column removed in v2.0 migration
            // Keeping parameter for backward compatibility but ignoring it
            if (!empty($status_filter)) {
                // Legacy status filter - no longer used but kept for compatibility
                vedmg_log_info('DATABASE', 'Status filter ignored - enrollment_status column removed in v2.0');
            }
            
            if (!empty($classroom_filter)) {
                $where_conditions[] = 'e.google_classroom_id = %s';
                $where_values[] = $classroom_filter;
            }

            // Add search functionality
            if (!empty($search_query)) {
                $search_term = '%' . $wpdb->esc_like($search_query) . '%';
                $where_conditions[] = '(
                    e.student_name LIKE %s OR
                    e.student_email LIKE %s OR
                    e.instructor_name LIKE %s OR
                    c.course_name LIKE %s OR
                    e.google_classroom_id LIKE %s
                )';
                // Add the search term 5 times for each field
                $where_values = array_merge($where_values, array($search_term, $search_term, $search_term, $search_term, $search_term));
            }

            $where_clause = implode(' AND ', $where_conditions);
            
            // Get total count for pagination
            $count_sql = "
                SELECT COUNT(*) 
                FROM $enrollments_table e
                LEFT JOIN $courses_table c ON e.course_id = c.course_id
                WHERE $where_clause
            ";
            
            if (!empty($where_values)) {
                $count_sql = $wpdb->prepare($count_sql, $where_values);
            }
            
            $total_count = $wpdb->get_var($count_sql);
            $total_pages = ceil($total_count / $per_page);
            
            // Calculate offset for pagination
            $offset = ($page - 1) * $per_page;
            
            // Get enrollments with student and course information (v2.0 schema)
            $sql = "
                SELECT
                    e.enrollment_id,
                    e.student_id,
                    e.student_name,
                    e.student_email,
                    e.student_phone,
                    e.instructor_name,
                    e.course_id,
                    e.woocommerce_order_id,
                    e.google_classroom_id,
                    e.purchase_date,
                    e.enrollment_date,
                    e.completion_date,
                    e.last_scheduled_date,
                    e.total_sessions_scheduled,
                    e.last_session_type,
                    COALESCE(c.course_name, 'Unknown Course') as course_name,
                    c.classroom_status,
                    'valid_user' as user_status
                FROM $enrollments_table e
                LEFT JOIN $courses_table c ON e.course_id = c.course_id
                WHERE $where_clause
                ORDER BY e.enrollment_date DESC, e.purchase_date DESC
                LIMIT %d OFFSET %d
            ";
            
            // Prepare the final query with pagination
            $pagination_values = array_merge($where_values, array($per_page, $offset));
            $sql = $wpdb->prepare($sql, $pagination_values);
            
            $enrollments = $wpdb->get_results($sql);
            
            vedmg_log_database('SELECT', 'vedmg_student_enrollments', 'Retrieved ' . count($enrollments) . ' enrollments (page ' . $page . ' of ' . $total_pages . ')');
            
            return array(
                'enrollments' => $enrollments,
                'total_count' => intval($total_count),
                'page' => $page,
                'per_page' => $per_page,
                'total_pages' => $total_pages
            );
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching student enrollments', $e->getMessage());
            return array(
                'enrollments' => array(),
                'total_count' => 0,
                'page' => $page,
                'per_page' => $per_page,
                'total_pages' => 0
            );
        }
    }
    
    /**
     * Get class sessions from database with pagination support
     * 
     * @param int $page Current page number (1-based)
     * @param int $per_page Number of items per page
     * @param string $course_filter Optional course filter
     * @param string $status_filter Optional status filter
     * @return array Array containing sessions, total_count, and total_pages
     */
    public static function get_class_sessions($page = 1, $per_page = 10, $course_filter = '', $status_filter = '') {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';

        try {
            // Check if tables exist first
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");

            if (!$sessions_exists || !$courses_exists) {
                vedmg_log_warning('DATABASE', 'Required tables do not exist yet');
                return array(
                    'sessions' => array(),
                    'total_count' => 0,
                    'total_pages' => 0
                );
            }

            // CRITICAL FIX: Ensure all required columns exist before querying
            self::ensure_sessions_table_columns($sessions_table);

            // Sanitize pagination parameters
            $page = max(1, intval($page));
            $per_page = max(1, min(100, intval($per_page)));
            
            // Build WHERE clause for filters
            $where_conditions = array();
            $where_values = array();
            
            if (!empty($course_filter)) {
                $where_conditions[] = "s.course_id = %d";
                $where_values[] = intval($course_filter);
            }
            
            if (!empty($status_filter)) {
                $where_conditions[] = "s.session_status = %s";
                $where_values[] = $status_filter;
            }
            
            $where_clause = '';
            if (!empty($where_conditions)) {
                $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            }

            // Get total count for pagination
            $count_sql = "
                SELECT COUNT(*)
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
                LEFT JOIN {$wpdb->users} assigned_u ON s.assigned_instructor_id = assigned_u.ID
                $where_clause
            ";
            
            if (!empty($where_values)) {
                $count_sql = $wpdb->prepare($count_sql, $where_values);
            }
            
            $total_count = intval($wpdb->get_var($count_sql));
            $total_pages = ceil($total_count / $per_page);

            // Calculate offset for pagination
            $offset = ($page - 1) * $per_page;
            
            // Get sessions with course information and pagination
            $sql = "
                SELECT
                    s.session_id,
                    s.course_id,
                    s.google_classroom_id,
                    s.session_title,
                    s.session_description,
                    s.google_meet_link,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.attendance_required,
                    s.max_participants,
                    s.assigned_instructor_id,
                    s.created_date,
                    s.is_featured,
                    s.featured_date,
                    c.course_name,
                    c.instructor_id,
                    COALESCE(c.instructor_name, u.display_name, 'Unknown') as instructor_name,
                    assigned_u.display_name as assigned_instructor_name
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
                LEFT JOIN {$wpdb->users} assigned_u ON s.assigned_instructor_id = assigned_u.ID
                $where_clause
                ORDER BY
                    CASE s.session_status
                        WHEN 'google classroom' THEN 1
                        WHEN 'ongoing' THEN 2
                        WHEN 'scheduled' THEN 3
                        WHEN 'completed' THEN 4
                        WHEN 'cancelled' THEN 5
                        ELSE 6
                    END,
                    s.created_date DESC
                LIMIT %d OFFSET %d
            ";
            
            // Prepare the final query with pagination
            $pagination_values = array_merge($where_values, array($per_page, $offset));
            $sql = $wpdb->prepare($sql, $pagination_values);
            
            $sessions = $wpdb->get_results($sql);
            
            vedmg_log_database('SELECT', 'vedmg_class_sessions', "Retrieved " . count($sessions) . " sessions (page $page of $total_pages)");
            
            return array(
                'sessions' => $sessions,
                'total_count' => $total_count,
                'total_pages' => $total_pages
            );
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching class sessions', $e->getMessage());
            return array(
                'sessions' => array(),
                'total_count' => 0,
                'total_pages' => 0
            );
        }
    }

    /**
     * CRITICAL FIX: Ensure all required columns exist in sessions table
     * This function adds missing columns that might not exist due to failed upgrades
     *
     * @param string $sessions_table The sessions table name
     */
    private static function ensure_sessions_table_columns($sessions_table) {
        global $wpdb;

        try {
            // Get current table structure
            $columns = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table");
            $existing_columns = array();
            foreach ($columns as $column) {
                $existing_columns[] = $column->Field;
            }

            // Define required columns with their SQL definitions
            $required_columns = array(
                'assigned_instructor_id' => 'BIGINT(20) UNSIGNED DEFAULT NULL',
                'is_featured' => 'TINYINT(1) DEFAULT 0',
                'featured_date' => 'DATETIME DEFAULT NULL',
                'session_type' => "ENUM('individual','group','class') DEFAULT 'individual'",
                'duration_minutes' => 'INT(11) DEFAULT 60',
                'is_recurring' => 'TINYINT(1) DEFAULT 0',
                'recurring_pattern' => 'VARCHAR(50) DEFAULT NULL',
                'recurring_days' => 'JSON DEFAULT NULL',
                'recurring_dates' => 'JSON DEFAULT NULL',
                'recurring_count' => 'INT(11) DEFAULT 1',
                'recurring_end_date' => 'DATE DEFAULT NULL',
                'selected_student_ids' => 'JSON DEFAULT NULL',
                'meeting_type' => "ENUM('individual', 'class') DEFAULT 'class'",
                'target_student_id' => 'INT NULL'
            );

            // Add missing columns
            foreach ($required_columns as $column_name => $column_definition) {
                if (!in_array($column_name, $existing_columns)) {
                    $sql = "ALTER TABLE $sessions_table ADD COLUMN $column_name $column_definition";
                    $result = $wpdb->query($sql);
                    if ($result !== false) {
                        vedmg_log_database('ALTER', 'vedmg_class_sessions', "Added missing column: $column_name");
                    } else {
                        vedmg_log_warning('DATABASE', "Failed to add column $column_name: " . $wpdb->last_error);
                    }
                }
            }

            // Ensure session_status enum includes 'google classroom'
            $status_column = $wpdb->get_row("SHOW COLUMNS FROM $sessions_table LIKE 'session_status'");
            if ($status_column && strpos($status_column->Type, 'google classroom') === false) {
                $sql = "ALTER TABLE $sessions_table MODIFY COLUMN session_status ENUM('scheduled','ongoing','completed','cancelled','google classroom') DEFAULT 'scheduled'";
                $result = $wpdb->query($sql);
                if ($result !== false) {
                    vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Updated session_status enum to include google classroom');
                }
            }

            vedmg_log_info('DATABASE', 'Sessions table column verification completed');

        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error ensuring sessions table columns', $e->getMessage());
        }
    }

    /**
     * Get all instructors from database
     * Fetches ALL users with instructor role, not just those with courses
     *
     * @return array Array of instructor objects
     */
    public static function get_instructors() {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Get ALL users with instructor role (including those without courses)
            $sql = "
                SELECT 
                    u.ID as instructor_id,
                    u.display_name as instructor_name,
                    u.user_email as instructor_email,
                    um_phone.meta_value as instructor_phone,
                    um_specialization.meta_value as specialization,
                    COALESCE(course_data.course_count, 0) as course_count,
                    COALESCE(course_data.student_count, 0) as student_count,
                    u.user_registered as join_date,
                    CASE 
                        WHEN COALESCE(course_data.course_count, 0) > 0 THEN 'active'
                        ELSE 'inactive'
                    END as status,
                    COALESCE(course_data.last_activity, u.user_registered) as last_activity
                FROM {$wpdb->users} u
                INNER JOIN {$wpdb->usermeta} um_role ON (u.ID = um_role.user_id AND um_role.meta_key = '{$wpdb->prefix}capabilities' AND um_role.meta_value LIKE '%instructor%')
                LEFT JOIN {$wpdb->usermeta} um_phone ON (u.ID = um_phone.user_id AND um_phone.meta_key = 'billing_phone')
                LEFT JOIN {$wpdb->usermeta} um_specialization ON (u.ID = um_specialization.user_id AND um_specialization.meta_key = 'instructor_specialization')
                LEFT JOIN (
                    SELECT 
                        instructor_id,
                        COUNT(course_id) as course_count,
                        SUM(
                            (SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_enrollments e 
                             WHERE e.course_id = c.course_id)
                        ) as student_count,
                        MAX(updated_date) as last_activity
                    FROM {$courses_table} c
                    WHERE instructor_id IS NOT NULL
                    GROUP BY instructor_id
                ) course_data ON u.ID = course_data.instructor_id
                GROUP BY u.ID
                ORDER BY course_data.course_count DESC, u.display_name ASC
            ";
            
            $instructors = $wpdb->get_results($sql);
            
            vedmg_log_database('SELECT', 'instructors', 'Retrieved ' . count($instructors) . ' instructors (including those without courses)');
            
            return $instructors;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructors', $e->getMessage());
            
            // Fallback: Get instructors the old way (only those with courses)
            try {
                $sql_fallback = "
                    SELECT 
                        u.ID as instructor_id,
                        u.display_name as instructor_name,
                        u.user_email as instructor_email,
                        um_phone.meta_value as instructor_phone,
                        um_specialization.meta_value as specialization,
                        COUNT(c.course_id) as course_count,
                        COALESCE(SUM(
                            (SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_enrollments e 
                             WHERE e.course_id = c.course_id)
                        ), 0) as student_count,
                        u.user_registered as join_date,
                        'active' as status,
                        COALESCE(MAX(c.updated_date), u.user_registered) as last_activity
                    FROM {$wpdb->users} u
                    LEFT JOIN {$courses_table} c ON u.ID = c.instructor_id
                    LEFT JOIN {$wpdb->usermeta} um_phone ON (u.ID = um_phone.user_id AND um_phone.meta_key = 'billing_phone')
                    LEFT JOIN {$wpdb->usermeta} um_specialization ON (u.ID = um_specialization.user_id AND um_specialization.meta_key = 'instructor_specialization')
                    WHERE u.ID IN (SELECT DISTINCT instructor_id FROM {$courses_table} WHERE instructor_id IS NOT NULL)
                    GROUP BY u.ID, u.display_name, u.user_email, um_phone.meta_value, um_specialization.meta_value, u.user_registered
                    ORDER BY course_count DESC, u.display_name ASC
                ";
                
                $instructors = $wpdb->get_results($sql_fallback);
                vedmg_log_warning('DATABASE', 'Used fallback query for instructors - only those with courses');
                
                return $instructors;
                
            } catch (Exception $e2) {
                vedmg_log_error('DATABASE', 'Fallback instructor query also failed', $e2->getMessage());
                return array();
            }
        }
    }

    /**
     * Get instructors with pagination and filtering
     * Fetches ALL users with instructor role with proper pagination
     * 
     * @param int $page Current page number (1-based)
     * @param int $per_page Number of items per page
     * @param string $status_filter Filter by status (active/inactive)
     * @param string $search_term Search term for name/email
     * @return array Array with 'instructors' and 'total' count
     */
    public static function get_instructors_paginated($page = 1, $per_page = 10, $status_filter = '', $search_term = '') {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Calculate offset for pagination
            $offset = ($page - 1) * $per_page;
            
            // Build WHERE clause for filtering
            $where_conditions = array();
            $where_params = array();
            
            // Base condition: users with instructor role
            $where_conditions[] = "um_role.meta_key = %s AND um_role.meta_value LIKE %s";
            $where_params[] = $wpdb->prefix . 'capabilities';
            $where_params[] = '%instructor%';
            
            // Add search filter
            if (!empty($search_term)) {
                $where_conditions[] = "(u.display_name LIKE %s OR u.user_email LIKE %s)";
                $where_params[] = '%' . $wpdb->esc_like($search_term) . '%';
                $where_params[] = '%' . $wpdb->esc_like($search_term) . '%';
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // Build main query
            $main_sql = "
                SELECT 
                    u.ID as instructor_id,
                    u.display_name as instructor_name,
                    u.user_email as instructor_email,
                    um_phone.meta_value as instructor_phone,
                    um_specialization.meta_value as specialization,
                    COALESCE(course_data.course_count, 0) as course_count,
                    COALESCE(course_data.student_count, 0) as student_count,
                    u.user_registered as join_date,
                    CASE 
                        WHEN COALESCE(course_data.course_count, 0) > 0 THEN 'active'
                        ELSE 'inactive'
                    END as status,
                    COALESCE(course_data.last_activity, u.user_registered) as last_activity
                FROM {$wpdb->users} u
                INNER JOIN {$wpdb->usermeta} um_role ON (u.ID = um_role.user_id)
                LEFT JOIN {$wpdb->usermeta} um_phone ON (u.ID = um_phone.user_id AND um_phone.meta_key = 'billing_phone')
                LEFT JOIN {$wpdb->usermeta} um_specialization ON (u.ID = um_specialization.user_id AND um_specialization.meta_key = 'instructor_specialization')
                LEFT JOIN (
                    SELECT 
                        instructor_id,
                        COUNT(course_id) as course_count,
                        SUM(
                            (SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_enrollments e 
                             WHERE e.course_id = c.course_id)
                        ) as student_count,
                        MAX(updated_date) as last_activity
                    FROM {$courses_table} c
                    WHERE instructor_id IS NOT NULL
                    GROUP BY instructor_id
                ) course_data ON u.ID = course_data.instructor_id
                {$where_clause}
                GROUP BY u.ID
            ";
            
            // Add status filter to WHERE clause instead of HAVING clause
            if (!empty($status_filter)) {
                if ($status_filter === 'active') {
                    $where_conditions[] = "course_data.instructor_id IS NOT NULL";
                } elseif ($status_filter === 'inactive') {
                    $where_conditions[] = "course_data.instructor_id IS NULL";
                }
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // Get total count first
            $count_sql = "SELECT COUNT(*) FROM ({$main_sql}) as count_query";
            $total = intval($wpdb->get_var($wpdb->prepare($count_sql, $where_params)));
            
            // Get paginated results
            $results_sql = "{$main_sql} ORDER BY course_count DESC, u.display_name ASC LIMIT %d OFFSET %d";
            $all_params = array_merge($where_params, array($per_page, $offset));
            $instructors = $wpdb->get_results($wpdb->prepare($results_sql, $all_params));
            
            vedmg_log_database('SELECT', 'instructors_paginated', "Retrieved {$per_page} instructors (page {$page} of " . ceil($total / $per_page) . "), total: {$total}");
            
            return array(
                'instructors' => $instructors,
                'total' => $total
            );
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching paginated instructors', $e->getMessage());
            
            // Fallback: Get all instructors and simulate pagination
            try {
                $all_instructors = self::get_instructors();
                $total = count($all_instructors);
                $offset = ($page - 1) * $per_page;
                $instructors = array_slice($all_instructors, $offset, $per_page);
                
                vedmg_log_warning('DATABASE', 'Used fallback pagination for instructors');
                
                return array(
                    'instructors' => $instructors,
                    'total' => $total
                );
                
            } catch (Exception $e2) {
                vedmg_log_error('DATABASE', 'Fallback pagination also failed', $e2->getMessage());
                return array(
                    'instructors' => array(),
                    'total' => 0
                );
            }
        }
    }

    /**
     * Get instructor statistics for overview cards
     * Returns overall statistics for all instructors
     * 
     * @return array Array with statistics
     */
    public static function get_instructor_statistics() {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Get total instructors with instructor role
            $total_instructors = intval($wpdb->get_var($wpdb->prepare("
                SELECT COUNT(DISTINCT u.ID)
                FROM {$wpdb->users} u
                INNER JOIN {$wpdb->usermeta} um ON (u.ID = um.user_id AND um.meta_key = %s AND um.meta_value LIKE %s)
            ", $wpdb->prefix . 'capabilities', '%instructor%')));
            
            // Get active instructors (those with courses)
            $active_instructors = intval($wpdb->get_var("
                SELECT COUNT(DISTINCT instructor_id)
                FROM {$courses_table}
                WHERE instructor_id IS NOT NULL
            "));
            
            // Get total courses
            $total_courses = intval($wpdb->get_var("SELECT COUNT(*) FROM {$courses_table}"));
            
            // Get total students (sum of all enrollments)
            $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
            $total_students = 0;
            
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enrollments_table'");
            if ($table_exists) {
                $total_students = intval($wpdb->get_var("SELECT COUNT(*) FROM {$enrollments_table}"));
            }
            
            return array(
                'total_instructors' => $total_instructors,
                'active_instructors' => $active_instructors,
                'inactive_instructors' => $total_instructors - $active_instructors,
                'total_courses' => $total_courses,
                'total_students' => $total_students
            );
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructor statistics', $e->getMessage());
            return array(
                'total_instructors' => 0,
                'active_instructors' => 0,
                'inactive_instructors' => 0,
                'total_courses' => 0,
                'total_students' => 0
            );
        }
    }

    /**
     * Get instructor's scheduled sessions for today
     * Returns sessions scheduled for the instructor today
     * 
     * @param int $instructor_id Instructor ID
     * @return array Array of today's sessions
     */
    public static function get_instructor_today_sessions($instructor_id) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$sessions_exists || !$courses_exists) {
                return array();
            }
            
            $today = date('Y-m-d');
            
            $sql = "
                SELECT 
                    s.session_id,
                    s.session_title,
                    s.session_description,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.google_meet_link,
                    c.course_name,
                    c.course_id,
                    COUNT(e.enrollment_id) as enrolled_students
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->prefix}vedmg_student_enrollments e ON c.course_id = e.course_id
                WHERE c.instructor_id = %d 
                AND s.scheduled_date = %s
                AND s.session_status != 'cancelled'
                GROUP BY s.session_id
                ORDER BY s.start_time ASC
            ";
            
            $sessions = $wpdb->get_results($wpdb->prepare($sql, $instructor_id, $today));
            
            vedmg_log_database('SELECT', 'instructor_today_sessions', "Retrieved " . count($sessions) . " today's sessions for instructor $instructor_id");
            
            return $sessions;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructor today sessions', $e->getMessage());
            return array();
        }
    }
    
    /**
     * Get instructor's upcoming sessions (next 7 days)
     * Returns sessions scheduled for the instructor in the next week
     * 
     * @param int $instructor_id Instructor ID
     * @param int $limit Limit number of sessions to return
     * @return array Array of upcoming sessions
     */
    public static function get_instructor_upcoming_sessions($instructor_id, $limit = 10) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$sessions_exists || !$courses_exists) {
                return array();
            }
            
            $today = date('Y-m-d');
            $next_week = date('Y-m-d', strtotime('+7 days'));
            
            $sql = "
                SELECT 
                    s.session_id,
                    s.session_title,
                    s.session_description,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.google_meet_link,
                    c.course_name,
                    c.course_id,
                    COUNT(e.enrollment_id) as enrolled_students
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->prefix}vedmg_student_enrollments e ON c.course_id = e.course_id
                WHERE c.instructor_id = %d 
                AND s.scheduled_date >= %s 
                AND s.scheduled_date <= %s
                AND s.session_status != 'cancelled'
                GROUP BY s.session_id
                ORDER BY s.scheduled_date ASC, s.start_time ASC
                LIMIT %d
            ";
            
            $sessions = $wpdb->get_results($wpdb->prepare($sql, $instructor_id, $today, $next_week, $limit));
            
            vedmg_log_database('SELECT', 'instructor_upcoming_sessions', "Retrieved " . count($sessions) . " upcoming sessions for instructor $instructor_id");
            
            return $sessions;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructor upcoming sessions', $e->getMessage());
            return array();
        }
    }

    /**
     * Get ALL instructor's future sessions (including all scheduled recurring sessions)
     * This method shows all sessions from today onwards, not just next 7 days
     *
     * @param int $instructor_id Instructor ID
     * @param int $limit Limit number of sessions to return (default 50 for multi-week courses)
     * @return array Array of all future sessions
     */
    public static function get_instructor_all_future_sessions($instructor_id, $limit = 50) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';

        try {
            // Check if tables exist
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");

            if (!$sessions_exists || !$courses_exists) {
                return array();
            }

            $today = date('Y-m-d');
            $current_time = date('H:i:s');

            $sql = "
                SELECT
                    s.session_id,
                    s.session_title,
                    s.session_description,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.google_meet_link,
                    s.is_recurring,
                    s.recurrence_pattern,
                    c.course_name,
                    c.course_id,
                    COUNT(e.enrollment_id) as enrolled_students,
                    CASE
                        WHEN s.scheduled_date = %s AND s.start_time <= %s THEN 'today_current'
                        WHEN s.scheduled_date = %s AND s.start_time > %s THEN 'today_upcoming'
                        WHEN s.scheduled_date > %s THEN 'future'
                        ELSE 'past'
                    END as session_timing
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->prefix}vedmg_student_enrollments e ON c.course_id = e.course_id
                WHERE c.instructor_id = %d
                AND (
                    s.scheduled_date > %s
                    OR (s.scheduled_date = %s AND s.end_time >= %s)
                )
                AND s.session_status != 'cancelled'
                GROUP BY s.session_id
                ORDER BY s.scheduled_date ASC, s.start_time ASC
                LIMIT %d
            ";

            $sessions = $wpdb->get_results($wpdb->prepare(
                $sql,
                $today, $current_time, $today, $current_time, $today,  // For CASE statement
                $instructor_id, $today, $today, $current_time,         // For WHERE clause
                $limit
            ));

            vedmg_log_database('SELECT', 'instructor_all_future_sessions', "Retrieved " . count($sessions) . " future sessions for instructor $instructor_id");

            return $sessions;

        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructor all future sessions', $e->getMessage());
            return array();
        }
    }

    /**
     * Get instructor's future sessions for reassignment
     * Returns sessions scheduled for the instructor in the future
     * 
     * @param int $instructor_id Instructor ID
     * @return array Array of future sessions
     */
    public static function get_instructor_future_sessions($instructor_id) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$sessions_exists || !$courses_exists) {
                return array();
            }
            
            $today = date('Y-m-d');
            
            $sql = "
                SELECT 
                    s.session_id,
                    s.session_title,
                    s.session_description,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.google_meet_link,
                    c.course_name,
                    c.course_id,
                    COUNT(e.enrollment_id) as enrolled_students
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->prefix}vedmg_student_enrollments e ON c.course_id = e.course_id
                WHERE c.instructor_id = %d 
                AND s.scheduled_date >= %s
                AND s.session_status != 'cancelled'
                GROUP BY s.session_id
                ORDER BY s.scheduled_date ASC, s.start_time ASC
            ";
            
            $sessions = $wpdb->get_results($wpdb->prepare($sql, $instructor_id, $today));
            
            vedmg_log_database('SELECT', 'instructor_future_sessions', "Retrieved " . count($sessions) . " future sessions for instructor $instructor_id");
            
            return $sessions;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructor future sessions', $e->getMessage());
            return array();
        }
    }
    
    /**
     * Reassign instructor sessions to another instructor
     * Updates all future sessions of an instructor to be assigned to another instructor
     * 
     * @param int $from_instructor_id Source instructor ID
     * @param int $to_instructor_id Target instructor ID
     * @param array $session_ids Optional array of specific session IDs to reassign
     * @return array Result with success status and affected sessions count
     */
    public static function reassign_instructor_sessions($from_instructor_id, $to_instructor_id, $session_ids = array()) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            $wpdb->query('START TRANSACTION');
            
            $affected_sessions = 0;
            
            // Get new instructor details
            $new_instructor = get_userdata($to_instructor_id);
            $new_instructor_name = $new_instructor ? $new_instructor->display_name : 'Unknown';
            
            if (!empty($session_ids)) {
                // Reassign specific sessions
                // Find unique courses for these sessions
                $session_ids_str = implode(',', array_map('intval', $session_ids));
                $courses_to_update = $wpdb->get_col("
                    SELECT DISTINCT course_id 
                    FROM $sessions_table 
                    WHERE session_id IN ($session_ids_str)
                ");
                
                // Update instructor for these courses
                foreach ($courses_to_update as $course_id) {
                    $wpdb->update(
                        $courses_table,
                        array(
                            'instructor_id' => $to_instructor_id,
                            'instructor_name' => $new_instructor_name
                        ),
                        array('course_id' => $course_id),
                        array('%d', '%s'),
                        array('%d')
                    );
                }
                
                $affected_sessions = count($session_ids);
                
            } else {
                // Reassign all future sessions for this instructor
                $today = date('Y-m-d');
                
                // Update instructor for courses that have future sessions
                $result = $wpdb->query($wpdb->prepare("
                    UPDATE $courses_table 
                    SET instructor_id = %d, instructor_name = %s 
                    WHERE instructor_id = %d 
                    AND course_id IN (
                        SELECT DISTINCT course_id 
                        FROM $sessions_table 
                        WHERE scheduled_date >= %s 
                        AND session_status != 'cancelled'
                    )
                ", $to_instructor_id, $new_instructor_name, $from_instructor_id, $today));
                
                // Count affected sessions
                $affected_sessions = $wpdb->get_var($wpdb->prepare("
                    SELECT COUNT(*) 
                    FROM $sessions_table s
                    JOIN $courses_table c ON s.course_id = c.course_id
                    WHERE c.instructor_id = %d 
                    AND s.scheduled_date >= %s
                    AND s.session_status != 'cancelled'
                ", $to_instructor_id, $today));
            }
            
            $wpdb->query('COMMIT');
            
            vedmg_log_info('DATABASE', "Reassigned $affected_sessions sessions from instructor $from_instructor_id to $to_instructor_id");
            
            return array(
                'success' => true,
                'affected_sessions' => intval($affected_sessions),
                'message' => "Successfully reassigned $affected_sessions sessions"
            );
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            vedmg_log_error('DATABASE', 'Error reassigning instructor sessions', $e->getMessage());
            
            return array(
                'success' => false,
                'affected_sessions' => 0,
                'message' => 'Failed to reassign sessions: ' . $e->getMessage()
            );
        }
    }

    /**
     * Reassign meetings from one instructor to another
     * Specifically for individual meeting assignments that are stored in class_sessions table
     * 
     * @param int $from_instructor_id The instructor to reassign from
     * @param int $to_instructor_id The instructor to reassign to  
     * @param array $meeting_ids Array of specific meeting session IDs to reassign
     * @return array Result with success status and affected count
     */
    public static function reassign_instructor_meetings($from_instructor_id, $to_instructor_id, $meeting_ids = array()) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            $wpdb->query('START TRANSACTION');
            
            $affected_meetings = 0;
            
            // Get new instructor details
            $new_instructor = get_userdata($to_instructor_id);
            $new_instructor_name = $new_instructor ? $new_instructor->display_name : 'Unknown';
            
            if (!empty($meeting_ids)) {
                // Reassign specific meetings (sessions with meeting links)
                $meeting_ids_str = implode(',', array_map('intval', $meeting_ids));
                
                // Update sessions that have meeting links - these are individual meetings
                $result = $wpdb->query($wpdb->prepare("
                    UPDATE $sessions_table 
                    SET session_title = CONCAT(%s, ' - ', session_title)
                    WHERE session_id IN ($meeting_ids_str)
                    AND google_meet_link IS NOT NULL 
                    AND google_meet_link != ''
                ", $new_instructor_name));
                
                if ($result !== false) {
                    $affected_meetings = $wpdb->rows_affected;
                    
                    // Also update the courses table for these meetings if needed
                    $courses_table = $wpdb->prefix . 'vedmg_courses';
                    $courses_to_update = $wpdb->get_col("
                        SELECT DISTINCT course_id 
                        FROM $sessions_table 
                        WHERE session_id IN ($meeting_ids_str)
                        AND google_meet_link IS NOT NULL 
                        AND google_meet_link != ''
                    ");
                    
                    // Update instructor for these courses
                    foreach ($courses_to_update as $course_id) {
                        $wpdb->update(
                            $courses_table,
                            array(
                                'instructor_id' => $to_instructor_id,
                                'instructor_name' => $new_instructor_name
                            ),
                            array('course_id' => $course_id),
                            array('%d', '%s'),
                            array('%d')
                        );
                    }
                }
            }
            
            $wpdb->query('COMMIT');
            
            vedmg_log_info('DATABASE', "Reassigned $affected_meetings meetings from instructor $from_instructor_id to $to_instructor_id");
            
            return array(
                'success' => true,
                'affected_meetings' => intval($affected_meetings),
                'message' => "Successfully reassigned $affected_meetings meetings"
            );
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            vedmg_log_error('DATABASE', 'Error reassigning instructor meetings', $e->getMessage());
            
            return array(
                'success' => false,
                'affected_meetings' => 0,
                'message' => 'Failed to reassign meetings: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get database statistics for admin dashboard
     * Returns counts of records in each table for quick overview
     * 
     * @return array Array with counts for each table
     */
    public static function get_dashboard_stats() {
        global $wpdb;
        
        $stats = array(
            'courses' => 0,
            'enrollments' => 0,
            'sessions' => 0,
            'active_classrooms' => 0,
            'pending_enrollments' => 0
        );
        
        try {
            // Count courses
            $courses_table = $wpdb->prefix . 'vedmg_courses';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            if ($table_exists) {
                $stats['courses'] = intval($wpdb->get_var("SELECT COUNT(*) FROM $courses_table"));
                $stats['active_classrooms'] = intval($wpdb->get_var("SELECT COUNT(*) FROM $courses_table WHERE classroom_status = 'active'"));
            }
            
            // Count enrollments
            $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enrollments_table'");
            if ($table_exists) {
                $stats['enrollments'] = intval($wpdb->get_var("SELECT COUNT(*) FROM $enrollments_table"));
                $stats['pending_enrollments'] = intval($wpdb->get_var("SELECT COUNT(*) FROM $enrollments_table WHERE enrollment_status = 'pending'"));
            }
            
            // Count sessions
            $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            if ($table_exists) {
                $stats['sessions'] = intval($wpdb->get_var("SELECT COUNT(*) FROM $sessions_table"));
            }
            
            vedmg_log_database('SELECT', 'multiple', 'Retrieved dashboard statistics');
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching dashboard statistics', $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * Get available Google Classroom options for dropdowns
     * Returns list of available classrooms for enrollment selection
     * 
     * @return array Array of classroom options
     */
    public static function get_classroom_options() {
        global $wpdb;
        
        $mappings_table = $wpdb->prefix . 'vedmg_student_classroom_mappings';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist first
            $mappings_exists = $wpdb->get_var("SHOW TABLES LIKE '$mappings_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$mappings_exists || !$courses_exists) {
                vedmg_log_warning('DATABASE', 'Required tables do not exist yet');
                return array();
            }
            
            // Get unique classrooms from mappings table joined with courses
            $sql = "
                SELECT DISTINCT
                    m.google_classroom_id,
                    m.course_id,
                    COALESCE(c.course_name, CONCAT('Classroom ', m.google_classroom_id)) as course_name,
                    'active' as classroom_status
                FROM $mappings_table m
                LEFT JOIN $courses_table c ON m.course_id = c.course_id
                WHERE m.google_classroom_id IS NOT NULL 
                AND m.google_classroom_id != ''
                
                UNION
                
                SELECT DISTINCT
                    c.google_classroom_id,
                    c.course_id,
                    c.course_name,
                    c.classroom_status
                FROM $courses_table c
                WHERE c.google_classroom_id IS NOT NULL 
                AND c.google_classroom_id != ''
                AND c.classroom_status IN ('created', 'active')
                AND c.google_classroom_id NOT IN (
                    SELECT DISTINCT google_classroom_id 
                    FROM $mappings_table 
                    WHERE google_classroom_id IS NOT NULL
                )
                
                ORDER BY course_name ASC
            ";
            
            $classrooms = $wpdb->get_results($sql);
            
            vedmg_log_database('SELECT', 'classroom_options', 'Retrieved ' . count($classrooms) . ' classroom options from database');
            
            return $classrooms;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching classroom options', $e->getMessage());
            return array();
        }
    }
    
    /**
     * Format enrollment status for display
     * Update enrollment scheduling data (v2.0)
     * Updates scheduling tracking fields when a new session is scheduled
     */
    public static function update_enrollment_scheduling($enrollment_id, $session_type, $session_details = null) {
        global $wpdb;

        try {
            $table_name = $wpdb->prefix . 'vedmg_student_enrollments';

            // Get current scheduling data
            $current_data = $wpdb->get_row($wpdb->prepare(
                "SELECT total_sessions_scheduled FROM $table_name WHERE enrollment_id = %d",
                $enrollment_id
            ));

            $new_session_count = ($current_data->total_sessions_scheduled ?? 0) + 1;

            // Update scheduling tracking fields
            $result = $wpdb->update(
                $table_name,
                [
                    'last_scheduled_date' => current_time('mysql'),
                    'total_sessions_scheduled' => $new_session_count,
                    'last_session_type' => $session_type,
                    'updated_date' => current_time('mysql')
                ],
                ['enrollment_id' => $enrollment_id],
                ['%s', '%d', '%s', '%s'],
                ['%d']
            );

            if ($result === false) {
                throw new Exception('Failed to update enrollment scheduling data: ' . $wpdb->last_error);
            }

            vedmg_log_info('DATABASE', "Updated scheduling data for enrollment $enrollment_id", [
                'session_type' => $session_type,
                'new_session_count' => $new_session_count
            ]);

            return true;

        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Failed to update enrollment scheduling data', [
                'enrollment_id' => $enrollment_id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Add session tracking record (v2.0)
     * Creates detailed tracking record for session management
     */
    public static function add_session_tracking($enrollment_id, $student_id, $course_id, $session_id, $session_type, $scheduled_date, $session_details = null, $google_event_id = null) {
        global $wpdb;

        try {
            $table_name = $wpdb->prefix . 'vedmg_session_tracking';

            $result = $wpdb->insert(
                $table_name,
                [
                    'enrollment_id' => $enrollment_id,
                    'student_id' => $student_id,
                    'course_id' => $course_id,
                    'session_id' => $session_id,
                    'session_type' => $session_type,
                    'scheduled_date' => $scheduled_date,
                    'session_details' => $session_details ? json_encode($session_details) : null,
                    'google_event_id' => $google_event_id,
                    'created_date' => current_time('mysql')
                ],
                ['%d', '%d', '%d', '%d', '%s', '%s', '%s', '%s', '%s']
            );

            if ($result === false) {
                throw new Exception('Failed to add session tracking record: ' . $wpdb->last_error);
            }

            $tracking_id = $wpdb->insert_id;

            vedmg_log_info('DATABASE', "Added session tracking record $tracking_id", [
                'enrollment_id' => $enrollment_id,
                'session_type' => $session_type,
                'google_event_id' => $google_event_id
            ]);

            return $tracking_id;

        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Failed to add session tracking record', [
                'enrollment_id' => $enrollment_id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Format session status for display
     * Converts database session status to user-friendly display format
     * 
     * @param string $status Database session status value
     * @return string Formatted status with CSS class
     */
    public static function format_session_status($status) {
        $status_map = array(
            'scheduled' => '<span class="vedmg-status-pending">Scheduled</span>',
            'ongoing' => '<span class="vedmg-status-active">Ongoing</span>',
            'completed' => '<span class="vedmg-status-inactive">Completed</span>',
            'cancelled' => '<span class="vedmg-status-inactive">Cancelled</span>',
            'google classroom' => '<span class="vedmg-status-success">Google Classroom</span>'
        );
        
        return isset($status_map[$status]) ? $status_map[$status] : '<span class="vedmg-status-pending">' . ucfirst($status) . '</span>';
    }
    
    /**
     * Format created date for display in Indian timezone with 12-hour format
     * 
     * @param string $datetime MySQL datetime string
     * @return string Formatted date and time in Indian timezone
     */
    public static function format_created_date($datetime) {
        if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
            return 'Not Available';
        }
        
        // Create DateTime object from MySQL datetime
        $date = new DateTime($datetime, new DateTimeZone('UTC'));
        
        // Convert to Indian timezone
        $date->setTimezone(new DateTimeZone('Asia/Kolkata'));
        
        // Format in 12-hour format with Indian date format
        return $date->format('d M Y, h:i A');
    }
    
    /**
     * Format classroom status for display
     * Converts database status to user-friendly display format
     * 
     * @param string $status Database status value
     * @return string Formatted status with CSS class
     */
    public static function format_classroom_status($status) {
        $status_map = array(
            'pending' => '<span class="vedmg-status-pending">Pending</span>',
            'created' => '<span class="vedmg-status-active">Created</span>',
            'active' => '<span class="vedmg-status-active">Active</span>',
            'archived' => '<span class="vedmg-status-inactive">Archived</span>'
        );
        
        return isset($status_map[$status]) ? $status_map[$status] : '<span class="vedmg-status-pending">' . ucfirst($status) . '</span>';
    }
    
    /**
     * Format date for display
     * Converts database datetime to user-friendly format
     * 
     * @param string $datetime Database datetime value
     * @return string Formatted date
     */
    public static function format_date($datetime) {
        if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
            return 'Not set';
        }
        
        return date('M j, Y', strtotime($datetime));
    }
    
    /**
     * Format time for display
     * Converts database time to user-friendly format
     * 
     * @param string $time Database time value
     * @return string Formatted time
     */
    public static function format_time($time) {
        if (empty($time) || $time === '00:00:00') {
            return 'Not set';
        }
        
        return date('g:i A', strtotime($time));
    }
    
    /**
     * Get course by ID
     * 
     * @param int $course_id Course ID
     * @return object|null Course data or null if not found
     */
    public static function get_course_by_id($course_id) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $courses_table WHERE course_id = %d",
                $course_id
            ));
            
            vedmg_log_info('DATABASE', "Retrieved course $course_id: " . ($course ? 'Found' : 'Not found'));
            
            return $course;
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error getting course by ID: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Update course in database
     * 
     * @param int $course_id Course ID
     * @param array $course_data Course data to update
     * @return bool Success status
     */
    public static function update_course($course_id, $course_data) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Courses table does not exist - cannot update');
                return false;
            }
            
            // Prepare update data
            $update_data = array();
            $update_format = array();
            
            if (isset($course_data['course_name'])) {
                $update_data['course_name'] = $course_data['course_name'];
                $update_format[] = '%s';
            }
            
            if (isset($course_data['course_description'])) {
                $update_data['course_description'] = $course_data['course_description'];
                $update_format[] = '%s';
            }
            
            if (isset($course_data['instructor_name'])) {
                $update_data['instructor_name'] = $course_data['instructor_name'];
                $update_format[] = '%s';
            }
            
            if (isset($course_data['classroom_status'])) {
                $update_data['classroom_status'] = $course_data['classroom_status'];
                $update_format[] = '%s';
            }
            
            // Add updated timestamp
            $update_data['updated_date'] = date('Y-m-d H:i:s');
            $update_format[] = '%s';
            
            // Perform update
            $result = $wpdb->update(
                $courses_table,
                $update_data,
                array('course_id' => $course_id),
                $update_format,
                array('%d')
            );
            
            if ($result !== false) {
                vedmg_log_info('DATABASE', "Successfully updated course $course_id");
                vedmg_log_admin_action("Course $course_id updated with data: " . json_encode($course_data));
                return true;
            } else {
                vedmg_log_error('DATABASE', "Failed to update course $course_id: " . $wpdb->last_error);
                return false;
            }
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error updating course: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update instructor in database
     * 
     * @param int $instructor_id Instructor ID
     * @param array $instructor_data Instructor data to update
     * @return bool Success status
     */
    public static function update_instructor($instructor_id, $instructor_data) {
        global $wpdb;
        
        $instructors_table = $wpdb->prefix . 'vedmg_instructors';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$instructors_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Instructors table does not exist - cannot update');
                return false;
            }
            
            // Prepare update data
            $update_data = array();
            $update_format = array();
            
            if (isset($instructor_data['instructor_name'])) {
                $update_data['instructor_name'] = $instructor_data['instructor_name'];
                $update_format[] = '%s';
            }
            
            if (isset($instructor_data['instructor_email'])) {
                $update_data['instructor_email'] = $instructor_data['instructor_email'];
                $update_format[] = '%s';
            }
            
            if (isset($instructor_data['instructor_phone'])) {
                $update_data['instructor_phone'] = $instructor_data['instructor_phone'];
                $update_format[] = '%s';
            }
            
            if (isset($instructor_data['instructor_bio'])) {
                $update_data['instructor_bio'] = $instructor_data['instructor_bio'];
                $update_format[] = '%s';
            }
            
            if (isset($instructor_data['status'])) {
                $update_data['status'] = $instructor_data['status'];
                $update_format[] = '%s';
            }
            
            // Add updated timestamp
            $update_data['last_activity'] = date('Y-m-d H:i:s');
            $update_format[] = '%s';
            
            // Perform update
            $result = $wpdb->update(
                $instructors_table,
                $update_data,
                array('instructor_id' => $instructor_id),
                $update_format,
                array('%d')
            );
            
            if ($result !== false) {
                vedmg_log_info('DATABASE', "Successfully updated instructor $instructor_id");
                vedmg_log_admin_action("Instructor $instructor_id updated with data: " . json_encode($instructor_data));
                return true;
            } else {
                vedmg_log_error('DATABASE', "Failed to update instructor $instructor_id: " . $wpdb->last_error);
                return false;
            }
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error updating instructor: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update enrollment in database
     * 
     * @param int $enrollment_id Enrollment ID
     * @param array $enrollment_data Enrollment data to update
     * @return bool Success status
     */
    public static function update_enrollment($enrollment_id, $enrollment_data) {
        global $wpdb;
        
        $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enrollments_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Enrollments table does not exist - cannot update');
                return false;
            }
            
            // Prepare update data
            $update_data = array();
            $update_format = array();
            
            if (isset($enrollment_data['student_name'])) {
                $update_data['student_name'] = $enrollment_data['student_name'];
                $update_format[] = '%s';
            }
            
            if (isset($enrollment_data['student_email'])) {
                $update_data['student_email'] = $enrollment_data['student_email'];
                $update_format[] = '%s';
            }
            
            if (isset($enrollment_data['course_id'])) {
                $update_data['course_id'] = $enrollment_data['course_id'];
                $update_format[] = '%d';
            }
            
            if (isset($enrollment_data['enrollment_status'])) {
                $update_data['enrollment_status'] = $enrollment_data['enrollment_status'];
                $update_format[] = '%s';
            }
            
            if (isset($enrollment_data['enrollment_date'])) {
                $update_data['enrollment_date'] = $enrollment_data['enrollment_date'];
                $update_format[] = '%s';
            }
            
            // Perform update
            $result = $wpdb->update(
                $enrollments_table,
                $update_data,
                array('enrollment_id' => $enrollment_id),
                $update_format,
                array('%d')
            );
            
            if ($result !== false) {
                vedmg_log_info('DATABASE', "Successfully updated enrollment $enrollment_id");
                vedmg_log_admin_action("Enrollment $enrollment_id updated with data: " . json_encode($enrollment_data));
                return true;
            } else {
                vedmg_log_error('DATABASE', "Failed to update enrollment $enrollment_id: " . $wpdb->last_error);
                return false;
            }
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error updating enrollment: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update session in database
     * 
     * @param int $session_id Session ID
     * @param array $session_data Session data to update
     * @return bool Success status
     */
    public static function update_session($session_id, $session_data) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Validate session ID
            if (!$session_id || !is_numeric($session_id)) {
                vedmg_log_error('DATABASE', 'Invalid session ID provided for update: ' . $session_id);
                return false;
            }
            
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Sessions table does not exist - cannot update');
                return false;
            }
            
            // Check if session exists
            $session_exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $sessions_table WHERE session_id = %d",
                $session_id
            ));
            
            if (!$session_exists) {
                vedmg_log_error('DATABASE', "Session $session_id does not exist - cannot update");
                return false;
            }
            
            // Prepare update data
            $update_data = array();
            $update_format = array();
            
            if (isset($session_data['session_title'])) {
                $update_data['session_title'] = $session_data['session_title'];
                $update_format[] = '%s';
            }
            
            if (isset($session_data['session_description'])) {
                $update_data['session_description'] = $session_data['session_description'];
                $update_format[] = '%s';
            }
            
            if (isset($session_data['course_id'])) {
                $update_data['course_id'] = $session_data['course_id'];
                $update_format[] = '%d';
            }
            
            if (isset($session_data['session_date'])) {
                $update_data['scheduled_date'] = $session_data['session_date'];
                $update_format[] = '%s';
            }
            
            if (isset($session_data['session_time'])) {
                $update_data['start_time'] = $session_data['session_time'];
                $update_format[] = '%s';
            }
            
            if (isset($session_data['end_time'])) {
                $update_data['end_time'] = $session_data['end_time'];
                $update_format[] = '%s';
            }
            
            if (isset($session_data['status'])) {
                // Map frontend status values to database ENUM values
                $status_mapping = array(
                    'scheduled' => 'scheduled',
                    'active' => 'ongoing',
                    'completed' => 'completed',
                    'cancelled' => 'cancelled',
                    'google classroom' => 'google classroom'
                );
                
                $mapped_status = isset($status_mapping[$session_data['status']]) 
                    ? $status_mapping[$session_data['status']] 
                    : 'scheduled';
                    
                $update_data['session_status'] = $mapped_status;
                $update_format[] = '%s';
            }
            
            // Add updated timestamp
            $update_data['updated_date'] = date('Y-m-d H:i:s');
            $update_format[] = '%s';
            
            // Ensure we have some data to update
            if (empty($update_data) || count($update_data) <= 1) { // Only timestamp
                vedmg_log_warning('DATABASE', 'No valid data provided for session update');
                return false;
            }
            
            // Log what we're trying to update
            vedmg_log_info('DATABASE', "Attempting to update session $session_id with data: " . json_encode($update_data));
            
            // Perform update
            $result = $wpdb->update(
                $sessions_table,
                $update_data,
                array('session_id' => $session_id),
                $update_format,
                array('%d')
            );
            
            if ($result !== false) {
                vedmg_log_info('DATABASE', "Successfully updated session $session_id. Rows affected: $result");
                vedmg_log_admin_action("Session $session_id updated with data: " . json_encode($session_data));
                return true;
            } else {
                vedmg_log_error('DATABASE', "Failed to update session $session_id. MySQL error: " . $wpdb->last_error);
                vedmg_log_error('DATABASE', "Update query: " . $wpdb->last_query);
                return false;
            }
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error updating session: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store student classroom mapping data
     * 
     * @param array $mapping_data Student classroom mapping data
     * @return bool Success status
     */
    public static function store_student_classroom_mapping($mapping_data) {
        global $wpdb;
        
        $mappings_table = $wpdb->prefix . 'vedmg_student_classroom_mappings';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$mappings_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Student classroom mappings table does not exist');
                return false;
            }
            
            // Prepare data for insertion
            $insert_data = array(
                'student_id' => intval($mapping_data['student_id']),
                'student_email' => sanitize_email($mapping_data['student_email']),
                'google_classroom_id' => sanitize_text_field($mapping_data['google_classroom_id']),
                'course_id' => intval($mapping_data['course_id']),
                'enrollment_status' => sanitize_text_field($mapping_data['enrollment_status'] ?? 'enrolled'),
                'fetched_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            );
            
            $insert_format = array('%d', '%s', '%s', '%d', '%s', '%s', '%s');
            
            // Check if mapping already exists
            $existing_mapping = $wpdb->get_row($wpdb->prepare("
                SELECT id FROM $mappings_table 
                WHERE student_email = %s AND google_classroom_id = %s
            ", $insert_data['student_email'], $insert_data['google_classroom_id']));
            
            if ($existing_mapping) {
                // Update existing mapping
                $update_data = array(
                    'enrollment_status' => $insert_data['enrollment_status'],
                    'updated_at' => $insert_data['updated_at']
                );
                
                $result = $wpdb->update(
                    $mappings_table,
                    $update_data,
                    array('id' => $existing_mapping->id),
                    array('%s', '%s'),
                    array('%d')
                );
                
                if ($result !== false) {
                    vedmg_log_database('UPDATE', 'vedmg_student_classroom_mappings', "Updated mapping for student {$insert_data['student_email']}");
                    return true;
                }
            } else {
                // Insert new mapping
                $result = $wpdb->insert($mappings_table, $insert_data, $insert_format);
                
                if ($result !== false) {
                    vedmg_log_database('INSERT', 'vedmg_student_classroom_mappings', "Created mapping for student {$insert_data['student_email']}");
                    return true;
                }
            }
            
            vedmg_log_error('DATABASE', 'Failed to store student classroom mapping. MySQL error: ' . $wpdb->last_error);
            return false;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error storing student classroom mapping: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get student classroom mappings from database
     * 
     * @param string $student_email Optional student email filter
     * @return array Array of mappings
     */
    public static function get_student_classroom_mappings($student_email = '') {
        global $wpdb;
        
        $mappings_table = $wpdb->prefix . 'vedmg_student_classroom_mappings';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$mappings_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Student classroom mappings table does not exist');
                return array();
            }
            
            $sql = "
                SELECT 
                    m.*,
                    c.course_name,
                    c.classroom_status
                FROM $mappings_table m
                LEFT JOIN {$wpdb->prefix}vedmg_courses c ON m.course_id = c.course_id
            ";
            
            if (!empty($student_email)) {
                $sql .= $wpdb->prepare(" WHERE m.student_email = %s", $student_email);
            }
            
            $sql .= " ORDER BY m.updated_at DESC";
            
            $mappings = $wpdb->get_results($sql);
            
            vedmg_log_database('SELECT', 'vedmg_student_classroom_mappings', 'Retrieved ' . count($mappings) . ' mappings');
            
            return $mappings;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching student classroom mappings: ' . $e->getMessage());
            return array();
        }
    }
    
    /**
     * Create recurring sessions based on recurrence pattern
     * 
     * @param array $base_session Base session data
     * @param array $recurrence_pattern Recurrence configuration
     * @return bool Success status
     */
    public static function create_recurring_sessions($base_session, $recurrence_pattern) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Sessions table does not exist');
                return false;
            }
            
            $created_count = 0;
            $frequency = $recurrence_pattern['frequency'] ?? 'weekly';
            $days = $recurrence_pattern['days'] ?? array();
            $end_date = $recurrence_pattern['end_date'] ?? '';
            $max_occurrences = $recurrence_pattern['max_occurrences'] ?? 10;
            
            $start_date = new DateTime($base_session['scheduled_date']);
            $end_date_obj = !empty($end_date) ? new DateTime($end_date) : null;
            
            // Generate sessions based on frequency
            if ($frequency === 'weekly' && !empty($days)) {
                $current_date = clone $start_date;
                $occurrence_count = 0;
                
                while ($occurrence_count < $max_occurrences) {
                    // Check if we've reached the end date
                    if ($end_date_obj && $current_date > $end_date_obj) {
                        break;
                    }
                    
                    // Check if current day is in the selected days
                    $current_day = strtolower($current_date->format('l'));
                    
                    if (in_array($current_day, $days)) {
                        // Create session for this date
                        $session_data = $base_session;
                        $session_data['scheduled_date'] = $current_date->format('Y-m-d');
                        $session_data['is_recurring'] = true;
                        $session_data['recurrence_pattern'] = json_encode($recurrence_pattern);
                        
                        // Remove session_id to create new record
                        unset($session_data['session_id']);
                        
                        $result = $wpdb->insert($sessions_table, $session_data);
                        
                        if ($result !== false) {
                            $created_count++;
                            $occurrence_count++;
                        }
                    }
                    
                    // Move to next day
                    $current_date->add(new DateInterval('P1D'));
                }
            }
            
            vedmg_log_database('INSERT', 'vedmg_class_sessions', "Created $created_count recurring sessions");
            
            return $created_count > 0;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error creating recurring sessions: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Auto-update session statuses based on current time
     * This function should be called regularly to keep session statuses current
     * 
     * @return int Number of sessions updated
     */
    public static function auto_update_session_statuses() {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            if (!$table_exists) {
                vedmg_log_warning('DATABASE', 'Sessions table does not exist - cannot auto-update statuses');
                return 0;
            }
            
            $current_datetime = current_time('mysql'); // WordPress current time in MySQL format
            $current_date = current_time('Y-m-d');
            $current_time = current_time('H:i:s');
            
            vedmg_log_info('DATABASE', 'Auto-updating session statuses. Current time: ' . $current_datetime);
            
            $updated_count = 0;
            
            // 1. Update sessions that should be completed (past end time)
            $completed_query = $wpdb->prepare("
                UPDATE $sessions_table 
                SET session_status = 'completed', updated_date = %s
                WHERE (scheduled_date < %s 
                    OR (scheduled_date = %s AND end_time <= %s))
                AND session_status IN ('scheduled', 'ongoing')
                AND session_status != 'cancelled'
            ", $current_datetime, $current_date, $current_date, $current_time);
            
            $completed_result = $wpdb->query($completed_query);
            if ($completed_result !== false) {
                $updated_count += $completed_result;
                if ($completed_result > 0) {
                    vedmg_log_info('DATABASE', "Updated $completed_result sessions to completed status");
                }
            }
            
            // 2. Update sessions that should be ongoing (between start and end time)
            $ongoing_query = $wpdb->prepare("
                UPDATE $sessions_table 
                SET session_status = 'ongoing', updated_date = %s
                WHERE scheduled_date = %s 
                AND start_time <= %s 
                AND end_time > %s
                AND session_status = 'scheduled'
                AND session_status != 'cancelled'
            ", $current_datetime, $current_date, $current_time, $current_time);
            
            $ongoing_result = $wpdb->query($ongoing_query);
            if ($ongoing_result !== false) {
                $updated_count += $ongoing_result;
                if ($ongoing_result > 0) {
                    vedmg_log_info('DATABASE', "Updated $ongoing_result sessions to ongoing status");
                }
            }
            
            // Log summary
            if ($updated_count > 0) {
                vedmg_log_admin_action("Auto-updated $updated_count session statuses based on current time");
            }
            
            return $updated_count;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error auto-updating session statuses: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get sessions with real-time status calculation
     * This function calculates status on-the-fly and updates the database
     * 
     * @param int $page Page number
     * @param int $per_page Items per page
     * @param string $course_filter Course ID filter
     * @param string $status_filter Status filter
     * @return array Sessions data with updated statuses
     */
    public static function get_class_sessions_with_auto_update($page = 1, $per_page = 10, $course_filter = '', $status_filter = '') {
        // First, auto-update all session statuses
        self::auto_update_session_statuses();
        
        // Then get the sessions with updated statuses
        return self::get_class_sessions($page, $per_page, $course_filter, $status_filter);
    }
    
    /**
     * Get only upcoming sessions (today and future, not completed/cancelled)
     * 
     * @param int $limit Maximum number of sessions to return
     * @return array Upcoming sessions
     */
    public static function get_upcoming_sessions_only($limit = 10) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$sessions_exists || !$courses_exists) {
                return array();
            }
            
            $current_date = date('Y-m-d');
            $current_time = date('H:i:s');
            
            // Get only upcoming sessions with proper filtering
            $sql = "
                SELECT 
                    s.session_id,
                    s.course_id,
                    s.session_title,
                    s.session_description,
                    s.google_meet_link,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.assigned_instructor_id,
                    c.course_name,
                    c.instructor_id,
                    u.display_name as instructor_name,
                    assigned_u.display_name as assigned_instructor_name
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
                LEFT JOIN {$wpdb->users} assigned_u ON s.assigned_instructor_id = assigned_u.ID
                WHERE (
                    (s.scheduled_date > %s) OR 
                    (s.scheduled_date = %s AND s.end_time > %s)
                )
                AND s.session_status IN ('scheduled', 'ongoing')
                ORDER BY 
                    CASE s.session_status 
                        WHEN 'ongoing' THEN 1 
                        WHEN 'scheduled' THEN 2 
                    END,
                    s.scheduled_date ASC, 
                    s.start_time ASC
                LIMIT %d
            ";
            
            $sessions = $wpdb->get_results($wpdb->prepare(
                $sql,
                $current_date,  // for future dates
                $current_date,  // for today's date comparison
                $current_time,  // for today's time comparison
                $limit
            ));
            
            vedmg_log_database('SELECT', 'vedmg_class_sessions', "Retrieved " . count($sessions) . " upcoming sessions");
            
            return $sessions;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching upcoming sessions', $e->getMessage());
            return array();
        }
    }
    
    /**
     * Store a new scheduled session in the database
     * 
     * @param array $session_data Array containing session details
     * @return int|false Session ID on success, false on failure
     */
    public static function store_scheduled_session($session_data) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // If it's a recurring session, calculate all session dates first
            if (isset($session_data['is_recurring']) && $session_data['is_recurring']) {
                return self::store_recurring_sessions($session_data);
            } else {
                // Store single session
                return self::store_single_session($session_data);
            }
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error storing scheduled session', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store a single session in the database
     * 
     * @param array $session_data Array containing session details
     * @return int|false Session ID on success, false on failure
     */
    private static function store_single_session($session_data) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Prepare session data for database insertion
            $data = array(
                'course_id' => intval($session_data['course_id']),
                'session_title' => sanitize_text_field($session_data['session_title']),
                'session_description' => sanitize_textarea_field($session_data['description'] ?? ''),
                'scheduled_date' => sanitize_text_field($session_data['session_date']),
                'start_time' => sanitize_text_field($session_data['session_time']),
                'session_status' => 'scheduled',
                'session_type' => sanitize_text_field($session_data['session_type']),
                'duration_minutes' => intval($session_data['duration']),
                'is_recurring' => 0,
                'selected_student_ids' => json_encode($session_data['selected_student_ids'] ?? []),
                'google_meet_link' => '', // Will be filled after API call
                'google_classroom_id' => sanitize_text_field($session_data['google_classroom_id'] ?? ''),
                'created_date' => current_time('mysql')
            );
            
            // Calculate end time
            $start_time = new DateTime($session_data['session_time']);
            $end_time = clone $start_time;
            $end_time->add(new DateInterval('PT' . intval($session_data['duration']) . 'M'));
            $data['end_time'] = $end_time->format('H:i:s');
            
            // Insert session into database
            $result = $wpdb->insert($sessions_table, $data);
            
            if ($result === false) {
                vedmg_log_error('DATABASE', 'Failed to insert session', $wpdb->last_error);
                return false;
            }
            
            $session_id = $wpdb->insert_id;
            vedmg_log_database('INSERT', 'vedmg_class_sessions', "Stored single session ID: $session_id");
            
            return $session_id;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error storing single session', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store recurring sessions with proper date calculation and session count limits
     * 
     * @param array $session_data Array containing session details
     * @return int|false First session ID on success, false on failure
     */
    private static function store_recurring_sessions($session_data) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Calculate session dates based on pattern and constraints
            $session_dates = self::calculate_recurring_session_dates($session_data);
            
            if (empty($session_dates)) {
                vedmg_log_error('DATABASE', 'No valid session dates calculated for recurring session');
                return false;
            }
            
            $first_session_id = null;
            $created_count = 0;
            
            // Create each session
            foreach ($session_dates as $date) {
                $individual_session_data = $session_data;
                $individual_session_data['session_date'] = $date;
                $individual_session_data['session_title'] = $session_data['session_title'] . ' - Session ' . ($created_count + 1);
                
                // Prepare session data for database insertion
                $data = array(
                    'course_id' => intval($individual_session_data['course_id']),
                    'session_title' => sanitize_text_field($individual_session_data['session_title']),
                    'session_description' => sanitize_textarea_field($individual_session_data['description'] ?? ''),
                    'scheduled_date' => $date,
                    'start_time' => sanitize_text_field($individual_session_data['session_time']),
                    'session_status' => 'scheduled',
                    'session_type' => sanitize_text_field($individual_session_data['session_type']),
                    'duration_minutes' => intval($individual_session_data['duration']),
                    'is_recurring' => 1,
                    'recurring_pattern' => sanitize_text_field($individual_session_data['recurring_pattern'] ?? ''),
                    'recurring_days' => json_encode($individual_session_data['recurring_days'] ?? []),
                    'recurring_dates' => json_encode($individual_session_data['recurring_dates'] ?? []),
                    'recurring_count' => intval($individual_session_data['recurring_count'] ?? 1),
                    'recurring_end_date' => sanitize_text_field($individual_session_data['recurring_end_date'] ?? ''),
                    'selected_student_ids' => json_encode($individual_session_data['selected_student_ids'] ?? []),
                    'google_meet_link' => '', // Will be filled after API call
                    'google_classroom_id' => sanitize_text_field($individual_session_data['google_classroom_id'] ?? ''),
                    'created_date' => current_time('mysql')
                );
                
                // Calculate end time
                $start_time = new DateTime($individual_session_data['session_time']);
                $end_time = clone $start_time;
                $end_time->add(new DateInterval('PT' . intval($individual_session_data['duration']) . 'M'));
                $data['end_time'] = $end_time->format('H:i:s');
                
                // Insert session into database
                $result = $wpdb->insert($sessions_table, $data);
                
                if ($result === false) {
                    vedmg_log_error('DATABASE', 'Failed to insert recurring session', $wpdb->last_error);
                    continue;
                }
                
                $session_id = $wpdb->insert_id;
                if ($first_session_id === null) {
                    $first_session_id = $session_id;
                }
                
                $created_count++;
                vedmg_log_database('INSERT', 'vedmg_class_sessions', "Stored recurring session ID: $session_id for date: $date");
            }
            
            vedmg_log_info('DATABASE', "Created $created_count recurring sessions. First session ID: $first_session_id");
            
            return $first_session_id;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error storing recurring sessions', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Calculate session dates for recurring sessions with proper constraints
     * 
     * @param array $session_data Array containing session details
     * @return array Array of date strings in Y-m-d format
     */
    private static function calculate_recurring_session_dates($session_data) {
        $dates = array();
        
        try {
            $start_date = new DateTime($session_data['session_date']);
            $end_date = new DateTime($session_data['recurring_end_date']);
            $max_sessions = intval($session_data['recurring_count']);
            $pattern = $session_data['recurring_pattern'];
            
            // Safety check
            if ($max_sessions > 100) {
                $max_sessions = 100;
            }
            
            $current_date = clone $start_date;
            $session_count = 0;
            
            // Add the first session
            $dates[] = $current_date->format('Y-m-d');
            $session_count++;
            
            // Calculate subsequent sessions based on pattern
            while ($session_count < $max_sessions && $current_date <= $end_date) {
                if ($pattern === 'weekly') {
                    $current_date = self::get_next_weekly_date($current_date, $session_data['recurring_days'] ?? []);
                } elseif ($pattern === 'bi-weekly') {
                    $current_date = self::get_next_biweekly_date($current_date, $session_data['recurring_days'] ?? []);
                } elseif ($pattern === 'monthly') {
                    $current_date = self::get_next_monthly_date($current_date, $session_data['recurring_dates'] ?? []);
                } else {
                    // Custom pattern - just add 7 days for now
                    $current_date->add(new DateInterval('P7D'));
                }
                
                // Check constraints
                if ($current_date <= $end_date && $session_count < $max_sessions) {
                    $dates[] = $current_date->format('Y-m-d');
                    $session_count++;
                } else {
                    break;
                }
            }
            
            vedmg_log_info('DATABASE', "Calculated $session_count sessions for recurring pattern: $pattern");
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error calculating recurring session dates', $e->getMessage());
        }
        
        return $dates;
    }
    
    /**
     * Get next weekly date based on selected days
     */
    private static function get_next_weekly_date($current_date, $selected_days) {
        $next_date = clone $current_date;
        $next_date->add(new DateInterval('P1D'));
        
        $day_map = array(
            'monday' => 1, 'tuesday' => 2, 'wednesday' => 3, 'thursday' => 4,
            'friday' => 5, 'saturday' => 6, 'sunday' => 0
        );
        
        for ($i = 0; $i < 14; $i++) { // Maximum 2 weeks to find next occurrence
            $current_day = strtolower($next_date->format('l'));
            if (in_array($current_day, $selected_days)) {
                return $next_date;
            }
            $next_date->add(new DateInterval('P1D'));
        }
        
        return $next_date;
    }
    
    /**
     * Get next bi-weekly date based on selected days
     */
    private static function get_next_biweekly_date($current_date, $selected_days) {
        $next_date = clone $current_date;
        $next_date->add(new DateInterval('P14D')); // Add 2 weeks
        
        return self::get_next_weekly_date($next_date, $selected_days);
    }
    
    /**
     * Get next monthly date based on selected dates
     */
    private static function get_next_monthly_date($current_date, $selected_dates) {
        $next_date = clone $current_date;
        
        // Find next valid date in the following months
        for ($month_offset = 1; $month_offset <= 12; $month_offset++) {
            $test_date = clone $current_date;
            $test_date->add(new DateInterval("P{$month_offset}M"));
            
            foreach ($selected_dates as $date) {
                if (checkdate($test_date->format('m'), $date, $test_date->format('Y'))) {
                    $candidate_date = clone $test_date;
                    $candidate_date->setDate($test_date->format('Y'), $test_date->format('m'), $date);
                    
                    if ($candidate_date > $current_date) {
                        return $candidate_date;
                    }
                }
            }
        }
        
        return $next_date;
    }
    
    /**
     * Get session details by ID
     *
     * @param int $session_id Session ID
     * @return object|null Session object or null if not found
     */
    public static function get_session_by_id($session_id) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';

        try {
            $session = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $sessions_table WHERE session_id = %d",
                $session_id
            ));

            if ($session) {
                // Decode JSON fields
                $session->recurring_days = json_decode($session->recurring_days ?? '[]', true);
                $session->recurring_dates = json_decode($session->recurring_dates ?? '[]', true);
                $session->selected_student_ids = json_decode($session->selected_student_ids ?? '[]', true);
            }

            return $session;
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching session by ID', $e->getMessage());
            return null;
        }
    }

    /**
     * Get session details with calendar information for deletion
     *
     * @param int $session_id Session ID
     * @return object|null Session object with calendar info or null if not found
     */
    public static function get_session_with_calendar_info($session_id) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';

        try {
            $session = $wpdb->get_row($wpdb->prepare(
                "SELECT s.*, c.calendar_id, c.instructor_email, c.instructor_name, c.course_name
                 FROM $sessions_table s
                 LEFT JOIN $courses_table c ON s.course_id = c.course_id
                 WHERE s.session_id = %d",
                $session_id
            ));

            return $session;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching session by ID', $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get sessions for a specific course or student
     * 
     * @param int $course_id Course ID (optional)
     * @param int $student_id Student ID (optional)
     * @return array Array of session objects
     */
    public static function get_sessions($course_id = null, $student_id = null) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            $where_conditions = array();
            $prepare_values = array();
            
            if ($course_id) {
                $where_conditions[] = "course_id = %d";
                $prepare_values[] = $course_id;
            }
            
            if ($student_id) {
                $where_conditions[] = "JSON_CONTAINS(selected_student_ids, %s)";
                $prepare_values[] = json_encode($student_id);
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            $sql = "SELECT * FROM $sessions_table $where_clause ORDER BY scheduled_date DESC, start_time DESC";
            
            if (!empty($prepare_values)) {
                $sessions = $wpdb->get_results($wpdb->prepare($sql, ...$prepare_values));
            } else {
                $sessions = $wpdb->get_results($sql);
            }
            
            // Decode JSON fields for each session
            foreach ($sessions as $session) {
                $session->recurring_days = json_decode($session->recurring_days ?? '[]', true);
                $session->recurring_dates = json_decode($session->recurring_dates ?? '[]', true);
                $session->selected_student_ids = json_decode($session->selected_student_ids ?? '[]', true);
            }
            
            return $sessions;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching sessions', $e->getMessage());
            return array();
        }
    }
    
    /**
     * Update session with Google Meet link after API call
     * 
     * @param int $session_id Session ID
     * @param string $meet_link Google Meet link
     * @return bool Success status
     */
    public static function update_session_meet_link($session_id, $meet_link) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            $result = $wpdb->update(
                $sessions_table,
                array('google_meet_link' => sanitize_url($meet_link)),
                array('session_id' => intval($session_id))
            );
            
            if ($result === false) {
                vedmg_log_error('DATABASE', 'Failed to update session meet link', $wpdb->last_error);
                return false;
            }
            
            vedmg_log_database('UPDATE', 'vedmg_class_sessions', "Updated meet link for session ID: $session_id");
            return true;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error updating session meet link', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if a student has scheduled sessions
     * 
     * @param int $student_id Student ID
     * @return bool True if student has scheduled sessions, false otherwise
     */
    public static function has_scheduled_sessions($student_id) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Use LIKE for better compatibility with different MySQL/MariaDB versions
            $count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $sessions_table 
                 WHERE selected_student_ids LIKE %s",
                '%' . intval($student_id) . '%'
            ));
            
            return intval($count) > 0;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error checking scheduled sessions for student', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get session details for a student with enrollment info
     * 
     * @param int $student_id Student ID
     * @return array|null Array of session details or null if not found
     */
    public static function get_student_session_details($student_id) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            // Use LIKE for better compatibility with different MySQL/MariaDB versions
            $sessions = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $sessions_table 
                 WHERE selected_student_ids LIKE %s
                 ORDER BY scheduled_date DESC",
                '%' . intval($student_id) . '%'
            ));
            
            if (!$sessions) {
                return null;
            }
            
            // Process sessions and get additional details
            $processed_sessions = [];
            foreach ($sessions as $session) {
                // Decode JSON fields
                $session->recurring_days = json_decode($session->recurring_days ?? '[]', true);
                $session->recurring_dates = json_decode($session->recurring_dates ?? '[]', true);
                $student_ids_raw = $session->selected_student_ids ?? '[]';
                $session->selected_student_ids = json_decode($student_ids_raw, true);
                
                // Ensure selected_student_ids is an array
                if (!is_array($session->selected_student_ids)) {
                    $session->selected_student_ids = [];
                }
                
                // Verify this session actually contains our student (avoid false positives from LIKE)
                if (!in_array(intval($student_id), $session->selected_student_ids)) {
                    continue;
                }
                
                // Get all enrolled students for this session
                $all_students = self::get_session_enrolled_students($session->session_id);
                $session->enrolled_students = $all_students;
                $session->total_enrolled = count($all_students);
                
                // Set default values for missing fields
                $session->student_name = 'Student ' . $student_id;
                $session->student_email = 'student' . $student_id . '@example.com';
                $session->course_name = 'Default Course';
                
                $processed_sessions[] = $session;
            }
            
            return empty($processed_sessions) ? null : $processed_sessions;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching student session details', $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all students enrolled in a specific session
     * 
     * @param int $session_id Session ID
     * @return array Array of student details
     */
    public static function get_session_enrolled_students($session_id) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $enrollments_table = $wpdb->prefix . 'vedmg_classroom_enrollments';
        
        try {
            // First get the session to extract student IDs
            $session = $wpdb->get_row($wpdb->prepare(
                "SELECT selected_student_ids FROM $sessions_table WHERE session_id = %d",
                $session_id
            ));
            
            if (!$session) {
                return [];
            }
            
            $student_ids = json_decode($session->selected_student_ids ?? '[]', true);
            
            if (empty($student_ids)) {
                return [];
            }
            
            // Create placeholders for IN query
            $placeholders = implode(',', array_fill(0, count($student_ids), '%d'));
            
            $students = $wpdb->get_results($wpdb->prepare(
                "SELECT DISTINCT student_id, student_name, student_email, course_name 
                 FROM $enrollments_table 
                 WHERE student_id IN ($placeholders)",
                ...$student_ids
            ));
            
            return $students ?: [];
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching session enrolled students', $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get featured sessions only
     * Returns sessions that are marked as featured (is_featured = 1)
     * 
     * @param int $limit Maximum number of featured sessions to return
     * @return array Array of featured session objects
     */
    public static function get_featured_sessions_only($limit = 3) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        try {
            // Check if tables exist first
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
            
            if (!$sessions_exists || !$courses_exists) {
                vedmg_log_warning('DATABASE', 'Required tables do not exist yet');
                return array();
            }

            $limit = max(1, intval($limit));
            
            // Get featured sessions with course information
            $sql = "
                SELECT
                    s.session_id,
                    s.course_id,
                    s.google_classroom_id,
                    s.session_title,
                    s.session_description,
                    s.google_meet_link,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.assigned_instructor_id,
                    s.created_date,
                    s.is_featured,
                    s.featured_date,
                    c.course_name,
                    c.instructor_id,
                    COALESCE(c.instructor_name, u.display_name, 'Unknown') as instructor_name,
                    assigned_u.display_name as assigned_instructor_name
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->users} u ON c.instructor_id = u.ID
                LEFT JOIN {$wpdb->users} assigned_u ON s.assigned_instructor_id = assigned_u.ID
                WHERE s.is_featured = 1
                ORDER BY s.featured_date DESC, s.created_date DESC
                LIMIT %d
            ";
            
            $sessions = $wpdb->get_results($wpdb->prepare($sql, $limit));
            
            vedmg_log_database('SELECT', 'vedmg_class_sessions', "Retrieved " . count($sessions) . " featured sessions");
            
            return $sessions ?: array();
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching featured sessions', $e->getMessage());
            return array();
        }
    }
    
    /**
     * Toggle session featured status
     * 
     * @param int $session_id Session ID to toggle
     * @param bool $is_featured Whether to feature (true) or unfeature (false) the session
     * @return bool Success status
     */
    public static function toggle_session_featured($session_id, $is_featured = true) {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        try {
            $session_id = intval($session_id);
            
            // Check if session exists
            $session_exists = $wpdb->get_var($wpdb->prepare(
                "SELECT session_id FROM $sessions_table WHERE session_id = %d",
                $session_id
            ));
            
            if (!$session_exists) {
                vedmg_log_error('DATABASE', 'Session not found for featured toggle: ' . $session_id);
                return false;
            }
            
            $update_data = array(
                'is_featured' => $is_featured ? 1 : 0,
                'updated_date' => current_time('mysql')
            );
            
            // Set featured_date only when featuring (not when unfeaturing)
            if ($is_featured) {
                $update_data['featured_date'] = current_time('mysql');
            } else {
                $update_data['featured_date'] = null;
            }
            
            $result = $wpdb->update(
                $sessions_table,
                $update_data,
                array('session_id' => $session_id),
                array('%d', '%s', '%s'),
                array('%d')
            );
            
            if ($result !== false) {
                $action = $is_featured ? 'featured' : 'unfeatured';
                vedmg_log_database('UPDATE', 'vedmg_class_sessions', "Session {$session_id} {$action} successfully");
                return true;
            } else {
                vedmg_log_error('DATABASE', 'Failed to toggle session featured status', $wpdb->last_error);
                return false;
            }
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error toggling session featured status', $e->getMessage());
            return false;
        }
    }

    /**
     * Format enrollment status for display
     *
     * @param string $status Raw status value
     * @return string Formatted status
     */
    public static function format_enrollment_status($status) {
        switch (strtolower($status)) {
            case 'enrolled':
                return 'Enrolled';
            case 'pending':
                return 'Pending';
            case 'completed':
                return 'Completed';
            case 'cancelled':
                return 'Cancelled';
            case 'expired':
                return 'Expired';
            default:
                return ucfirst($status);
        }
    }

    /**
     * Format instructor status for display
     *
     * @param string $status Raw status value
     * @return string Formatted status
     */
    public static function format_instructor_status($status) {
        switch (strtolower($status)) {
            case 'active':
                return 'Active';
            case 'inactive':
                return 'Inactive';
            case 'pending':
                return 'Pending Approval';
            case 'suspended':
                return 'Suspended';
            case 'on_leave':
                return 'On Leave';
            default:
                return ucfirst($status);
        }
    }

    /**
     * Get instructor's assigned meetings for reassignment
     * Returns meetings that are specifically assigned to an instructor
     *
     * @param int $instructor_id Instructor ID
     * @return array Array of assigned meetings
     */
    public static function get_instructor_meetings($instructor_id) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $courses_table = $wpdb->prefix . 'vedmg_courses';

        try {
            // Check if tables exist
            $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
            $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");

            if (!$sessions_exists || !$courses_exists) {
                return array();
            }

            $today = date('Y-m-d');

            $sql = "
                SELECT
                    s.session_id,
                    s.session_title,
                    s.session_description,
                    s.scheduled_date,
                    s.start_time,
                    s.end_time,
                    s.session_status,
                    s.google_meet_link,
                    c.course_name,
                    c.course_id,
                    COUNT(e.enrollment_id) as enrolled_students
                FROM $sessions_table s
                LEFT JOIN $courses_table c ON s.course_id = c.course_id
                LEFT JOIN {$wpdb->prefix}vedmg_student_enrollments e ON c.course_id = e.course_id
                WHERE c.instructor_id = %d
                AND s.scheduled_date >= %s
                AND s.google_meet_link IS NOT NULL
                AND s.google_meet_link != ''
                AND s.session_status != 'cancelled'
                GROUP BY s.session_id
                ORDER BY s.scheduled_date ASC, s.start_time ASC
            ";

            $meetings = $wpdb->get_results($wpdb->prepare($sql, $instructor_id, $today));

            vedmg_log_database('SELECT', 'instructor_meetings', "Retrieved " . count($meetings) . " assigned meetings for instructor $instructor_id");

            return $meetings;

        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Error fetching instructor meetings', $e->getMessage());
            return array();
        }
    }
}

?>
