<?php
/**
 * Debug script to check sessions table structure and data
 * Run this to diagnose the sessions display issue
 */

// WordPress environment setup
require_once('C:/xampp/htdocs/paylearn/wp-config.php');

global $wpdb;

echo "=== VedMG ClassRoom Sessions Debug ===\n\n";

// Check if sessions table exists
$sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");

if (!$table_exists) {
    echo "❌ Sessions table does not exist: $sessions_table\n";
    exit;
}

echo "✅ Sessions table exists: $sessions_table\n\n";

// Check table structure
echo "=== Table Structure ===\n";
$columns = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table");
foreach ($columns as $column) {
    echo "- {$column->Field} ({$column->Type})\n";
}

// Check for missing columns that the query expects
$expected_columns = ['is_featured', 'featured_date', 'assigned_instructor_id'];
$missing_columns = [];

foreach ($expected_columns as $expected) {
    $found = false;
    foreach ($columns as $column) {
        if ($column->Field === $expected) {
            $found = true;
            break;
        }
    }
    if (!$found) {
        $missing_columns[] = $expected;
    }
}

if (!empty($missing_columns)) {
    echo "\n❌ Missing columns: " . implode(', ', $missing_columns) . "\n";
} else {
    echo "\n✅ All expected columns are present\n";
}

// Check data count
$total_sessions = $wpdb->get_var("SELECT COUNT(*) FROM $sessions_table");
echo "\n=== Data Count ===\n";
echo "Total sessions in database: $total_sessions\n";

if ($total_sessions > 0) {
    echo "\n=== Sample Data ===\n";
    $sample_sessions = $wpdb->get_results("SELECT * FROM $sessions_table LIMIT 3");
    foreach ($sample_sessions as $session) {
        echo "Session ID: {$session->session_id}\n";
        echo "  Title: {$session->session_title}\n";
        echo "  Course ID: {$session->course_id}\n";
        echo "  Status: {$session->session_status}\n";
        echo "  Date: {$session->scheduled_date}\n";
        echo "  Created: {$session->created_date}\n";
        echo "\n";
    }
}

// Check database version
$db_version = get_option('vedmg_classroom_db_version', 'unknown');
echo "=== Database Version ===\n";
echo "Current DB version: $db_version\n";

// Test the actual query that's failing
echo "\n=== Testing Query ===\n";
try {
    // Try the query without the problematic columns first
    $basic_query = "SELECT session_id, session_title, course_id, session_status FROM $sessions_table LIMIT 1";
    $basic_result = $wpdb->get_results($basic_query);
    echo "✅ Basic query works: " . count($basic_result) . " results\n";
    
    // Now try with the problematic columns
    if (empty($missing_columns)) {
        $full_query = "SELECT session_id, session_title, course_id, session_status, is_featured, featured_date FROM $sessions_table LIMIT 1";
        $full_result = $wpdb->get_results($full_query);
        echo "✅ Full query works: " . count($full_result) . " results\n";
    } else {
        echo "❌ Cannot test full query due to missing columns\n";
    }
    
} catch (Exception $e) {
    echo "❌ Query failed: " . $e->getMessage() . "\n";
}

echo "\n=== Recommendations ===\n";
if (!empty($missing_columns)) {
    echo "1. Run database upgrade to add missing columns\n";
    echo "2. The upgrade should add: " . implode(', ', $missing_columns) . "\n";
} else if ($total_sessions == 0) {
    echo "1. No sessions found in database\n";
    echo "2. Try creating a test session to verify display\n";
} else {
    echo "1. Table structure looks correct\n";
    echo "2. Data exists in the table\n";
    echo "3. Check for PHP errors in the admin panel\n";
}

echo "\nDone.\n";
?>
