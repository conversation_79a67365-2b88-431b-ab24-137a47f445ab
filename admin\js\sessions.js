/**
 * VedMG ClassRoom Sessions JavaScript
 * 
 * JavaScript functionality specific to the class sessions page.
 * Handles session scheduling, management, and Google Meet integration.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

(function($) {
    'use strict';

    /**
     * Sessions management specific functionality
     */
    var VedMGSessions = {
        
        /**
         * Initialize session management functionality
         */
        init: function() {
            console.log('VedMG ClassRoom Sessions initialized');
            
            // Bind session specific events
            this.bindEvents();
            
            // Initialize session components
            this.initComponents();
            
            // Update session counts
            this.updateSessionCounts();
            
            // Initialize auto-refresh for session statuses
            this.initAutoRefresh();
        },
        
        /**
         * Bind session specific events
         */
        bindEvents: function() {
            // Session management actions
            $(document).on('click', '#schedule-session, #schedule-first-session', this.handleScheduleSession);
            $(document).on('click', '.vedmg-view-session-details-btn', this.handleViewSessionDetails);
            $(document).on('click', '.vedmg-cancel-delete-session-btn', this.handleCancelDeleteSession);
            $(document).on('click', '.vedmg-view-recording-btn', this.handleViewRecording);
            $(document).on('click', '.vedmg-join-meet-btn', this.handleJoinMeet);
            
            // Featured session actions
            $(document).on('click', '.vedmg-feature-session-btn', this.handleFeatureSession);
            $(document).on('click', '.vedmg-unfeature-session-btn', this.handleUnfeatureSession);
            $(document).on('click', '.vedmg-remove-featured-btn', this.handleRemoveFeatured);
            

            
            // Initialize session status updates on page load
            this.initSessionStatusUpdates();
            
            // Modal events
            $(document).on('click', '.vedmg-modal-close, #close-session-details, #close-session-details-btn, #cancel-session-schedule', this.closeModals);
            $(document).on('click', '.vedmg-modal', function(e) {
                if (e.target === this) {
                    VedMGSessions.closeModals();
                }
            });
            $(document).on('submit', '#vedmg-session-schedule-form', this.handleScheduleSessionSubmit);
            
            // Time calculation for scheduling
            $(document).on('change', '#schedule-session-start-time, #schedule-session-duration', this.handleTimeChange);
            
            // Pagination events
            $(document).on('click', '.vedmg-pagination-btn', this.handlePagination);
            // Per-page change handler for server-side pagination
            $(document).on('change', '#per_page', this.handlePageSizeChange);
            
            // Filtering
            $(document).on('click', '#apply-session-filters', this.handleApplyFilters);
            $(document).on('click', '#clear-session-filters', this.handleClearFilters);
            $(document).on('change', '#session-course-filter, #session-status-filter', this.handleFilterChange);
            $(document).on('change', '#session-date-filter', this.handleDateFilterChange);
            
            // Bulk actions
            $(document).on('click', '#apply-session-bulk-action', this.handleBulkAction);
            $(document).on('change', '#select-all-sessions', this.handleSelectAll);
            $(document).on('change', '.session-checkbox', this.handleSessionSelect);
            
            // Refresh sessions
            $(document).on('click', '#refresh-sessions', this.handleRefreshSessions);

            // Repair database
            $(document).on('click', '#repair-database, #repair-database-empty', this.handleRepairDatabase);
            
            console.log('Session events bound');
        },
        
        /**
         * Initialize session components
         */
        initComponents: function() {
            // Initialize session cards
            this.initSessionCards();
            
            // Initialize session table
            this.initSessionTable();
            
            // Initialize filters
            this.initFilters();
            
            // Initialize pagination
            this.initPagination();
            
            console.log('Session components initialized');
        },
        
        /**
         * Initialize session cards
         */
        initSessionCards: function() {
            $('.vedmg-session-card').each(function() {
                var $card = $(this);
                
                // Add hover effects
                $card.on('mouseenter', function() {
                    $(this).addClass('hover');
                });
                
                $card.on('mouseleave', function() {
                    $(this).removeClass('hover');
                });
            });
        },
        
        /**
         * Initialize session table
         */
        initSessionTable: function() {
            // Add row interactions
            $('.vedmg-classroom-table tbody tr').each(function() {
                var $row = $(this);
                var sessionId = $row.data('session-id');
                
                if (sessionId) {
                    $row.attr('data-session-id', sessionId);
                }
            });
        },
        
        /**
         * Initialize filters
         */
        initFilters: function() {
            // Set today's date as default for date filter
            var today = new Date().toISOString().split('T')[0];
            $('#session-date-filter').attr('min', today);
        },
        
        /**
         * Initialize pagination (Server-side)
         */
        initPagination: function() {
            // Server-side pagination - just log that it's initialized
            console.log('Server-side pagination initialized for sessions');
            
            // Make sure page size select maintains current value
            var urlParams = new URLSearchParams(window.location.search);
            var currentPerPage = urlParams.get('per_page') || '10';
            $('#per_page').val(currentPerPage);
        },
        


        /**
         * Handle view session details
         */
        handleViewSessionDetails: function(e) {
            e.preventDefault();

            var $button = $(this);
            var sessionId = $button.data('session-id');

            if (!sessionId) {
                alert('Session ID is required');
                return;
            }

            // Get session data from the row
            var $row = $button.closest('tr');
            var sessionData = VedMGSessions.extractSessionData($row);

            // Show session details modal
            VedMGSessions.showSessionDetailsModal(sessionData);
        },


        
        /**
         * Handle schedule session
         */
        handleScheduleSession: function(e) {
            e.preventDefault();
            
            // Open session schedule modal
            VedMGSessions.openSessionScheduleModal();
            
            console.log('Scheduling new session');
        },
        
        /**
         * Extract session data from table row
         */
        extractSessionData: function($element) {
            // Legacy method for backward compatibility
            if ($element.hasClass('vedmg-session-card')) {
                return this.extractSessionDataFromCard($element);
            } else {
                return this.extractSessionDataFromRow($element);
            }
        },
        
        extractSessionDataFromCard: function($card) {
            var sessionId = $card.data('session-id');
            
            // Try to get session ID from button if not on card
            if (!sessionId) {
                sessionId = $card.find('.vedmg-edit-session-btn').data('session-id');
            }
            
            return {
                id: sessionId,
                title: $card.find('.vedmg-session-title').text().trim() || $card.find('h4').first().text().trim(),
                course: $card.find('.vedmg-session-details p:contains("Course:")').text().replace('Course:', '').trim(),
                instructor: $card.find('.vedmg-session-details p:contains("Instructor:")').text().replace('Instructor:', '').trim(),
                date: $card.find('.vedmg-session-date').text().trim() || $card.find('.vedmg-session-time').text().split(' at ')[0],
                time: $card.find('.vedmg-session-start-time').text().trim() || $card.find('.vedmg-session-time').text().split(' at ')[1],
                status: $card.find('.vedmg-session-status').data('status') || 'scheduled'
            };
        },
        
        extractSessionDataFromRow: function($row) {
            // Extract comprehensive session data from table row
            var sessionDetailsCell = $row.find('.session-details-cell');
            var timingDiv = sessionDetailsCell.find('.session-timing');
            var datetimeDiv = timingDiv.find('.session-datetime');
            var timeRange = timingDiv.find('.time-range').text().trim();

            // Parse time range (e.g., "⏰ 2:00 PM - 3:00 PM")
            var times = timeRange.replace('⏰ ', '').split(' - ');
            var startTime = times[0] || '';
            var endTime = times[1] || '';

            // Get date from datetime div
            var dateText = datetimeDiv.find('strong').text().replace('📅 ', '').trim();

            // Get session data from edit button if available
            var editButton = $row.find('.vedmg-edit-session-btn');
            var sessionDataFromButton = {};
            if (editButton.length && editButton.data('session-data')) {
                sessionDataFromButton = editButton.data('session-data');
            }

            return {
                session_id: $row.data('session-id'),
                session_title: $row.find('td:nth-child(2) strong').text().trim(),
                course_name: $row.find('td:nth-child(3) .course-link').text().trim() || $row.find('td:nth-child(3)').text().trim(),
                course_id: $row.data('course-id'),
                instructor_name: $row.find('td:nth-child(4) .instructor-link').text().trim() || $row.find('td:nth-child(4)').text().trim(),
                created_date: $row.find('td:nth-child(5) .vedmg-session-created-date').text().trim(),
                session_status: $row.find('.vedmg-session-status').data('status') || 'scheduled',
                scheduled_date: sessionDataFromButton.scheduled_date || this.parseDisplayDate(dateText),
                start_time: sessionDataFromButton.start_time || this.parseDisplayTime(startTime),
                end_time: sessionDataFromButton.end_time || this.parseDisplayTime(endTime),
                session_description: sessionDataFromButton.session_description || '',
                google_meet_link: sessionDataFromButton.google_meet_link || $row.find('.vedmg-join-meet-btn').attr('href') || '',
                duration_text: timingDiv.find('.session-duration small').text().replace('Duration: ', '').trim(),
                time_status: timingDiv.find('.session-status-time strong').text().trim(),
                is_expired: $row.data('is-expired') === '1',
                is_ongoing: $row.data('is-ongoing') === '1'
            };
        },

        /**
         * Parse display date to database format
         */
        parseDisplayDate: function(displayDate) {
            // Convert "Dec 31, 2024" to "2024-12-31"
            if (!displayDate) return '';

            try {
                var date = new Date(displayDate);
                if (isNaN(date.getTime())) return '';

                var year = date.getFullYear();
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var day = String(date.getDate()).padStart(2, '0');

                return year + '-' + month + '-' + day;
            } catch (e) {
                console.error('Error parsing display date:', displayDate, e);
                return '';
            }
        },

        /**
         * Parse display time to database format
         */
        parseDisplayTime: function(displayTime) {
            // Convert "2:00 PM" to "14:00:00"
            if (!displayTime) return '';

            try {
                var time = displayTime.trim();
                var isPM = time.toLowerCase().includes('pm');
                var isAM = time.toLowerCase().includes('am');

                // Remove AM/PM
                time = time.replace(/\s*(am|pm)/gi, '');

                var parts = time.split(':');
                var hours = parseInt(parts[0]);
                var minutes = parseInt(parts[1]) || 0;

                // Convert to 24-hour format
                if (isPM && hours !== 12) {
                    hours += 12;
                } else if (isAM && hours === 12) {
                    hours = 0;
                }

                return String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0') + ':00';
            } catch (e) {
                console.error('Error parsing display time:', displayTime, e);
                return '';
            }
        },



        /**
         * Open session schedule modal
         */
        openSessionScheduleModal: function() {
            var $modal = $('#vedmg-session-schedule-modal');
            
            // Clear form
            $modal.find('form')[0].reset();
            
            // Set default date to today
            var today = new Date().toISOString().split('T')[0];
            $('#schedule-session-date').val(today);
            
            // Show modal
            $modal.fadeIn(300);
        },

        /**
         * Show session details modal
         */
        showSessionDetailsModal: function(sessionData) {
            var $modal = $('#vedmg-session-details-modal');
            var $content = $('#session-details-content');

            console.log('Showing session details for:', sessionData);

            // Use existing timing data if available, otherwise calculate
            var timeStatus = sessionData.time_status || 'Status not available';
            var durationText = sessionData.duration_text || 'Duration not available';

            // If we have proper date/time data, calculate timing
            if (sessionData.scheduled_date && sessionData.start_time && sessionData.end_time) {
                var scheduledDateTime = new Date(sessionData.scheduled_date + ' ' + sessionData.start_time);
                var endDateTime = new Date(sessionData.scheduled_date + ' ' + sessionData.end_time);
                var currentTime = new Date();

                var isUpcoming = currentTime < scheduledDateTime;
                var isOngoing = currentTime >= scheduledDateTime && currentTime <= endDateTime;
                var isExpired = currentTime > endDateTime;

                if (isUpcoming) {
                    timeStatus = '<span class="status-upcoming">⏰ Starts in ' + VedMGSessions.getTimeUntil(scheduledDateTime) + '</span>';
                } else if (isOngoing) {
                    timeStatus = '<span class="status-ongoing">🔴 LIVE - Ends in ' + VedMGSessions.getTimeUntil(endDateTime) + '</span>';
                } else {
                    timeStatus = '<span class="status-expired">⚫ Ended ' + VedMGSessions.getTimeSince(endDateTime) + ' ago</span>';
                }

                // Calculate duration
                var durationMs = endDateTime - scheduledDateTime;
                var durationMinutes = Math.floor(durationMs / (1000 * 60));
                durationText = durationMinutes >= 60 ?
                    Math.floor(durationMinutes / 60) + 'h ' + (durationMinutes % 60) + 'm' :
                    durationMinutes + ' minutes';
            }

            var detailsHtml = `
                <div class="session-details-grid">
                    <div class="detail-section">
                        <h4>📚 Session Information</h4>
                        <p><strong>Title:</strong> ${sessionData.session_title || 'No title'}</p>
                        <p><strong>Course:</strong>
                            <a href="admin.php?page=vedmg-classroom-courses&course_id=${sessionData.course_id || ''}" class="course-link">
                                ${sessionData.course_name || 'Unknown Course'}
                            </a>
                        </p>
                        <p><strong>Instructor:</strong>
                            <a href="admin.php?page=vedmg-classroom-enrollments&instructor=${encodeURIComponent(sessionData.instructor_name || '')}" class="instructor-link">
                                ${sessionData.instructor_name || 'Unknown'}
                            </a>
                        </p>
                        <p><strong>Description:</strong> ${sessionData.session_description || 'No description provided'}</p>
                    </div>

                    <div class="detail-section">
                        <h4>📅 Timing Details</h4>
                        <p><strong>Date:</strong> ${sessionData.scheduled_date ? VedMGSessions.formatDate(sessionData.scheduled_date) : 'Date not available'}</p>
                        <p><strong>Time:</strong> ${sessionData.start_time && sessionData.end_time ?
                            VedMGSessions.formatTime(sessionData.start_time) + ' - ' + VedMGSessions.formatTime(sessionData.end_time) :
                            'Time not available'}</p>
                        <p><strong>Duration:</strong> ${durationText}</p>
                        <p><strong>Status:</strong> ${timeStatus}</p>
                        <p><strong>Current State:</strong>
                            ${sessionData.is_ongoing ? '🔴 LIVE NOW' :
                              sessionData.is_expired ? '⚫ ENDED' : '⏰ UPCOMING'}
                        </p>
                    </div>

                    <div class="detail-section">
                        <h4>🔗 Meeting & System Details</h4>
                        <p><strong>Google Meet Link:</strong>
                            ${sessionData.google_meet_link ?
                                `<a href="${sessionData.google_meet_link}" target="_blank" class="meet-link">Join Meeting</a>` :
                                'No meeting link available'
                            }
                        </p>
                        <p><strong>Session ID:</strong> #${sessionData.session_id || 'Unknown'}</p>
                        <p><strong>Course ID:</strong> #${sessionData.course_id || 'Unknown'}</p>
                        <p><strong>Created:</strong> ${sessionData.created_date || 'Date not available'}</p>
                        <p><strong>Status:</strong> ${sessionData.session_status ? sessionData.session_status.toUpperCase() : 'Unknown'}</p>
                    </div>
                </div>
            `;

            $content.html(detailsHtml);

            // Set session ID for edit button
            $modal.find('.vedmg-edit-session-from-modal').data('session-id', sessionData.session_id);

            // Show modal
            $modal.addClass('vedmg-modal-show').show();
        },

        /**
         * Close modals
         */
        closeModals: function() {
            $('.vedmg-modal').fadeOut(300);
        },
        

        
        /**
         * Handle schedule session submit
         */
        handleScheduleSessionSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $saveBtn = $form.find('#save-session-schedule');
            var formData = new FormData($form[0]);
            
            // Show loading state
            $saveBtn.addClass('loading').prop('disabled', true);
            
            // Simulate save process
            setTimeout(function() {
                $saveBtn.removeClass('loading').prop('disabled', false);
                VedMGSessions.closeModals();
                VedMGClassRoomAdmin.showMessage('Session scheduled successfully!', 'success');
                
                // Refresh the page data
                VedMGSessions.refreshSessionData();
            }, 1500);
        },
        
        /**
         * Calculate end time based on start time and duration
         */
        calculateEndTime: function(startTime, durationMinutes) {
            var start = new Date('2000-01-01 ' + startTime);
            start.setMinutes(start.getMinutes() + durationMinutes);
            
            var hours = start.getHours().toString().padStart(2, '0');
            var minutes = start.getMinutes().toString().padStart(2, '0');
            
            return hours + ':' + minutes;
        },
        
        /**
         * Convert 12-hour time to 24-hour format
         */
        convertTo24Hour: function(time12h) {
            var time = time12h.replace(/\s/g, '');
            var hours = parseInt(time.substr(0, time.indexOf(':')));
            var minutes = time.substr(time.indexOf(':') + 1, 2);
            var modifier = time.substr(-2);
            
            if (hours === 12) hours = 0;
            if (modifier === 'PM') hours = hours + 12;
            
            return hours.toString().padStart(2, '0') + ':' + minutes;
        },
        
        /**
         * Refresh session data
         */
        refreshSessionData: function() {
            console.log('Refreshing session data...');
            VedMGClassRoomAdmin.showMessage('Session data refreshed!', 'info');
        },
        
        /**
         * Handle cancel/delete session
         */
        handleCancelDeleteSession: function(e) {
            e.preventDefault();

            var $button = $(this);
            var sessionId = $button.data('session-id');
            var sessionTitle = $button.data('session-title') || 'this session';
            var $row = $button.closest('tr');

            // Show custom warning popup
            VedMGSessions.showDeleteWarningModal(sessionId, sessionTitle, $button, $row);

            console.log('🗑️ Delete session requested:', sessionId, sessionTitle);
        },
        
        /**
         * Initialize session status updates based on current time
         */
        initSessionStatusUpdates: function() {
            console.log('🕐 Initializing session status updates...');
            
            var currentTime = new Date();
            
            // Check each session row for status-based updates
            $('.real-data-row').each(function() {
                var $row = $(this);
                var $statusSpan = $row.find('.vedmg-session-status');
                
                if ($statusSpan.length) {
                    var currentStatus = $statusSpan.data('status');
                    
                    // For Google Classroom sessions, no automatic status changes needed
                    // They remain in 'google classroom' status until manually changed
                    if (currentStatus === 'google classroom') {
                        // Keep the status as is - no automatic updates
                        console.log('� Google Classroom session:', $row.data('session-id'));
                    }
                    // For other session types, keep existing logic if needed
                    else if (currentStatus === 'scheduled' || currentStatus === 'ongoing') {
                        // These would need specific business logic if required
                        console.log('📋 Traditional session:', $row.data('session-id'), 'Status:', currentStatus);
                    }
                }
            });
        },
        
        /**
         * Mark session as completed and move to bottom
         */
        markSessionAsCompleted: function($row) {
            var $statusSpan = $row.find('.vedmg-session-status');
            var $tbody = $row.closest('tbody');
            
            // Update status
            $statusSpan.html('Completed').data('status', 'completed');
            
            // Add completed styling (black and white)
            $row.addClass('session-completed').css({
                'background-color': '#f8f9fa',
                'color': '#6c757d',
                'opacity': '0.8'
            });
            
            // Move to bottom of table
            $row.detach().appendTo($tbody);
            
            // Update the session status in database
            var sessionId = $row.data('session-id');
            VedMGSessions.updateSessionStatus(sessionId, 'completed');
        },
        
        /**
         * Handle view recording
         */
        handleViewRecording: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var sessionId = $button.data('session-id');
            var $row = $button.closest('tr');
            var sessionTitle = $row.find('td:nth-child(2) strong').text();
            
            // Open recording viewer
            VedMGSessions.viewSessionRecording(sessionId, sessionTitle);
            
            console.log('Viewing recording for session:', sessionId);
        },
        
        /**
         * Handle join meet
         */
        handleJoinMeet: function(e) {
            e.preventDefault();

            var $button = $(this);
            var sessionId = $button.data('session-id');
            var courseId = $button.data('course-id');

            if (!sessionId || !courseId) {
                alert('Session or course information is missing');
                return;
            }

            // Show loading state
            var originalText = $button.text();
            $button.text('Loading...').prop('disabled', true);

            console.log('Fetching meeting link for session:', sessionId, 'course:', courseId);

            // Fetch meeting link from courses
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_get_course_meeting_link',
                    course_id: courseId,
                    session_id: sessionId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    $button.text(originalText).prop('disabled', false);

                    if (response.success && response.data.meeting_link) {
                        // Track meet join event
                        VedMGSessions.trackMeetJoin(sessionId);

                        // Open meeting link in new tab
                        window.open(response.data.meeting_link, '_blank');
                    } else {
                        alert('Meeting link not available: ' + (response.data || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    $button.text(originalText).prop('disabled', false);
                    console.error('Error fetching meeting link:', error);
                    alert('Error fetching meeting link. Please try again.');
                }
            });
        },
        
        /**
         * Handle feature session
         */
        handleFeatureSession: function(e) {
            e.preventDefault();

            var $button = $(this);
            var sessionId = $button.data('session-id');
            var sessionTitle = $button.data('session-title');

            if (!sessionId) {
                alert('Session ID is required');
                return;
            }

            // Show loading state
            var originalText = $button.text();
            $button.text('Featuring...').prop('disabled', true);

            // Make AJAX call to feature session
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_feature_session',
                    session_id: sessionId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message and reload page
                        alert('Session "' + sessionTitle + '" has been featured successfully!');
                        window.location.reload();
                    } else {
                        alert('Error: ' + (response.data || 'Failed to feature session'));
                        $button.text(originalText).prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    alert('Error featuring session. Please try again.');
                    $button.text(originalText).prop('disabled', false);
                }
            });
        },
        
        /**
         * Handle unfeature session
         */
        handleUnfeatureSession: function(e) {
            e.preventDefault();

            var $button = $(this);
            var sessionId = $button.data('session-id');
            var sessionTitle = $button.data('session-title');

            if (!sessionId) {
                alert('Session ID is required');
                return;
            }

            // Show loading state
            var originalText = $button.text();
            $button.text('Removing...').prop('disabled', true);

            // Make AJAX call to unfeature session
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_unfeature_session',
                    session_id: sessionId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message and reload page
                        alert('Session "' + sessionTitle + '" has been removed from featured sessions!');
                        window.location.reload();
                    } else {
                        alert('Error: ' + (response.data || 'Failed to remove session from featured'));
                        $button.text(originalText).prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    alert('Error removing session from featured. Please try again.');
                    $button.text(originalText).prop('disabled', false);
                }
            });
        },
        
        /**
         * Handle remove featured (from featured section)
         */
        handleRemoveFeatured: function(e) {
            e.preventDefault();

            var $button = $(this);
            var sessionId = $button.data('session-id');
            var self = VedMGSessions; // Get reference to the main object

            if (!sessionId) {
                alert('Session ID is required');
                return;
            }

            if (confirm('Remove this session from featured sessions?')) {
                self.unfeatureSession(sessionId);
            }
        },
        
        /**
         * Handle apply filters
         */
        /**
         * Handle apply filters (Server-side)
         */
        handleApplyFilters: function(e) {
            // Let the form submit naturally to server for server-side filtering
            console.log('Applying server-side filters via form submission');
        },
        
        /**
         * Handle clear filters (Server-side)
         */
        handleClearFilters: function(e) {
            // Let the clear filters link redirect naturally to server
            console.log('Clearing filters via server-side redirect');
        },
        
        /**
         * Handle filter change
         */
        /**
         * Handle filter change (Server-side)
         */
        handleFilterChange: function() {
            // For server-side filtering, don't auto-apply - let user click Apply Filters button
            console.log('Filter changed, user should click Apply Filters for server-side filtering');
        },
        
        /**
         * Handle bulk action
         */
        handleBulkAction: function(e) {
            e.preventDefault();
            
            var action = $('#session-bulk-action-select').val();
            var selectedSessions = $('.session-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
            
            if (!action) {
                alert('Please select a bulk action.');
                return;
            }
            
            if (selectedSessions.length === 0) {
                alert('Please select at least one session.');
                return;
            }
            
            // Confirm bulk action
            var message = 'Perform "' + action + '" on ' + selectedSessions.length + ' selected session(s)?';
            if (!confirm(message)) {
                return;
            }
            
            // Perform bulk action
            VedMGSessions.performBulkAction(action, selectedSessions);
        },
        
        /**
         * Handle select all sessions
         */
        handleSelectAll: function() {
            var isChecked = $(this).is(':checked');
            $('.session-checkbox').prop('checked', isChecked);
            VedMGSessions.updateBulkActionState();
        },
        
        /**
         * Handle individual session selection
         */
        handleSessionSelect: function() {
            VedMGSessions.updateBulkActionState();
            
            // Update select all checkbox
            var totalCheckboxes = $('.session-checkbox').length;
            var checkedCheckboxes = $('.session-checkbox:checked').length;
            
            $('#select-all-sessions').prop('checked', totalCheckboxes === checkedCheckboxes);
        },
        
        /**
         * Handle refresh sessions
         */
        handleRefreshSessions: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Refreshing...');
            
            // Refresh sessions data
            VedMGSessions.refreshSessionsData().then(function() {
                $button.prop('disabled', false);
                $button.text('Refresh Sessions');
                
                // Update counts
                VedMGSessions.updateSessionCounts();
                
                VedMGClassRoomAdmin.showMessage('Sessions refreshed successfully!', 'success');
            }).catch(function(error) {
                console.error('Failed to refresh sessions:', error);
                
                $button.prop('disabled', false);
                $button.text('Refresh Sessions');
                
                VedMGClassRoomAdmin.showMessage('Failed to refresh sessions. Please try again.', 'error');
            });
        },

        /**
         * Handle repair database
         */
        handleRepairDatabase: function(e) {
            e.preventDefault();

            var $button = $(this);

            // Confirm action
            if (!confirm('This will repair the database tables and fix any structural issues. Continue?')) {
                return;
            }

            // Show loading state
            $button.prop('disabled', true);
            $button.html('<span class="vedmg-classroom-spinner"></span> Repairing...');

            // Call repair function
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'repair_sessions_table',
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    console.log('Database repair response:', response);

                    $button.prop('disabled', false);
                    $button.html('🔧 Repair Database');

                    if (response.success) {
                        var messages = response.data.messages || [];
                        var errors = response.data.errors || [];

                        if (errors.length > 0) {
                            VedMGClassRoomAdmin.showMessage('Repair completed with warnings: ' + errors.join(', '), 'warning');
                        } else {
                            VedMGClassRoomAdmin.showMessage('Database repair completed successfully! Refreshing page...', 'success');

                            // Refresh the page after a short delay
                            setTimeout(function() {
                                window.location.reload();
                            }, 2000);
                        }
                    } else {
                        VedMGClassRoomAdmin.showMessage('Database repair failed: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Database repair failed:', error);

                    $button.prop('disabled', false);
                    $button.html('🔧 Repair Database');

                    VedMGClassRoomAdmin.showMessage('Database repair failed. Please check the console for details.', 'error');
                }
            });
        },

        /**
         * Show delete warning modal
         */
        showDeleteWarningModal: function(sessionId, sessionTitle, $button, $row) {
            var modalHtml = `
                <div id="vedmg-delete-warning-modal" class="vedmg-modal" style="display: block;">
                    <div class="vedmg-modal-content" style="max-width: 500px;">
                        <div class="vedmg-modal-header">
                            <h3>⚠️ Delete Session Warning</h3>
                            <span class="vedmg-modal-close" id="close-delete-warning">&times;</span>
                        </div>
                        <div class="vedmg-modal-body">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 48px; color: #dc3545; margin-bottom: 20px;">🗑️</div>
                                <h4 style="color: #dc3545; margin-bottom: 15px;">Are you sure you want to delete this session?</h4>
                                <p style="margin-bottom: 10px;"><strong>Session:</strong> "${sessionTitle}"</p>
                                <p style="color: #666; margin-bottom: 20px;">This action will:</p>
                                <ul style="text-align: left; color: #666; margin-bottom: 20px;">
                                    <li>Delete the associated Google Calendar (if exists)</li>
                                    <li>Permanently remove the session from database</li>
                                    <li>This action cannot be undone</li>
                                </ul>
                            </div>
                            <div class="vedmg-form-actions" style="justify-content: center;">
                                <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-delete-session">Cancel</button>
                                <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-danger" id="confirm-delete-session"
                                        data-session-id="${sessionId}" data-session-title="${sessionTitle}">
                                    Yes, Delete Session
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            $('#vedmg-delete-warning-modal').remove();

            // Add modal to body
            $('body').append(modalHtml);

            // Bind events
            $('#close-delete-warning, #cancel-delete-session').on('click', function() {
                $('#vedmg-delete-warning-modal').remove();
            });

            $('#confirm-delete-session').on('click', function() {
                $('#vedmg-delete-warning-modal').remove();

                // Show loading state
                $button.prop('disabled', true);
                $button.text('Deleting...');

                // Delete session from database
                VedMGSessions.deleteSession(sessionId, $button, $row);
            });

            // Close on background click
            $('#vedmg-delete-warning-modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).remove();
                }
            });
        },

        /**
         * Delete session from database
         */
        deleteSession: function(sessionId, $button, $row) {
            // Make AJAX call to delete session
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'delete_class_session',
                    session_id: sessionId,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    console.log('🗑️ Delete session response:', response);
                    
                    if (response.success) {
                        // Remove the row from table with animation
                        $row.fadeOut(500, function() {
                            $row.remove();
                            VedMGSessions.updateSessionCounts();
                        });
                        
                        var sessionTitle = $button.data('session-title') || 'Session';
                        VedMGClassRoomAdmin.showMessage('Meeting "' + sessionTitle + '" and associated calendar have been deleted successfully.', 'success');
                    } else {
                        $button.prop('disabled', false);
                        $button.text('Cancel/Delete Meeting');
                        VedMGClassRoomAdmin.showMessage('Failed to delete meeting: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Delete session error:', error);
                    $button.prop('disabled', false);
                    $button.text('Cancel/Delete Meeting');
                    VedMGClassRoomAdmin.showMessage('Failed to delete meeting. Please try again.', 'error');
                }
            });
        },
        
        /**
         * Update session status in database
         */
        updateSessionStatus: function(sessionId, newStatus) {
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'update_session_status',
                    session_id: sessionId,
                    status: newStatus,
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    console.log('📊 Update status response:', response);
                    if (!response.success) {
                        console.error('Failed to update session status:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Update status error:', error);
                }
            });
        },
        
        /**
         * View session recording
         */
        viewSessionRecording: function(sessionId, sessionTitle) {
            var details = 'Session Recording: ' + sessionTitle + '\n\n';
            details += 'Recording Details:\n';
            details += '• Duration: 45 minutes\n';
            details += '• Quality: 1080p HD\n';
            details += '• File Size: 234 MB\n';
            details += '• Participants: 12 students\n\n';
            details += 'Available Actions:\n';
            details += '• Download recording\n';
            details += '• Share with students\n';
            details += '• Generate transcript\n';
            details += '• Export highlights';
            
            alert(details + '\n\n(This is a placeholder - real implementation would open recording viewer)');
        },
        
        /**
         * Track meet join event
         */
        trackMeetJoin: function(sessionId) {
            // Track analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'meet_join', {
                    'session_id': sessionId,
                    'page_title': document.title
                });
            }
            
            // Log to console for debugging
            console.log('Meet join tracked for session:', sessionId);
        },
        
        /**
         * Feature a session
         */
        featureSession: function(sessionId) {
            var self = this;
            
            console.log('Featuring session:', sessionId);
            console.log('AJAX URL:', vedmg_classroom_ajax.ajax_url);
            console.log('API Nonce:', vedmg_classroom_ajax.api_nonce);
            
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_feature_session',
                    nonce: vedmg_classroom_ajax.nonce,
                    session_id: sessionId
                },
                beforeSend: function() {
                    console.log('Starting feature session request...');
                    // Show loading state
                    $('.vedmg-feature-session-btn[data-session-id="' + sessionId + '"]')
                        .prop('disabled', true)
                        .text('Featuring...');
                },
                success: function(response) {
                    console.log('Feature session response:', response);
                    if (response.success) {
                        // Show success message
                        self.showNotice('Session featured successfully!', 'success');
                        
                        // Reload page to update the display
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        console.error('Feature session failed:', response.data);
                        self.showNotice('Failed to feature session: ' + response.data, 'error');
                        // Reset button
                        $('.vedmg-feature-session-btn[data-session-id="' + sessionId + '"]')
                            .prop('disabled', false)
                            .text('Feature This Session');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error featuring session:', error);
                    console.error('XHR Response:', xhr.responseText);
                    self.showNotice('Error featuring session. Please try again.', 'error');
                    // Reset button
                    $('.vedmg-feature-session-btn[data-session-id="' + sessionId + '"]')
                        .prop('disabled', false)
                        .text('Feature This Session');
                }
            });
        },
        
        /**
         * Unfeature a session
         */
        unfeatureSession: function(sessionId) {
            var self = this;
            
            console.log('Unfeaturing session:', sessionId);
            console.log('AJAX URL:', vedmg_classroom_ajax.ajax_url);
            console.log('API Nonce:', vedmg_classroom_ajax.api_nonce);
            
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_unfeature_session',
                    nonce: vedmg_classroom_ajax.nonce,
                    session_id: sessionId
                },
                beforeSend: function() {
                    console.log('Starting unfeature session request...');
                    // Show loading state
                    $('.vedmg-unfeature-session-btn[data-session-id="' + sessionId + '"], ' +
                      '.vedmg-remove-featured-btn[data-session-id="' + sessionId + '"]')
                        .prop('disabled', true)
                        .text('Removing...');
                },
                success: function(response) {
                    console.log('Unfeature session response:', response);
                    if (response.success) {
                        // Show success message
                        self.showNotice('Session removed from featured successfully!', 'success');
                        
                        // Reload page to update the display
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        console.error('Unfeature session failed:', response.data);
                        self.showNotice('Failed to remove featured session: ' + response.data, 'error');
                        // Reset button
                        $('.vedmg-unfeature-session-btn[data-session-id="' + sessionId + '"], ' +
                          '.vedmg-remove-featured-btn[data-session-id="' + sessionId + '"]')
                            .prop('disabled', false)
                            .each(function() {
                                var $btn = $(this);
                                if ($btn.hasClass('vedmg-unfeature-session-btn')) {
                                    $btn.text('Remove Featured');
                                } else {
                                    $btn.text('Remove Featured');
                                }
                            });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error unfeaturing session:', error);
                    console.error('XHR Response:', xhr.responseText);
                    self.showNotice('Error removing featured session. Please try again.', 'error');
                    // Reset button
                    $('.vedmg-unfeature-session-btn[data-session-id="' + sessionId + '"], ' +
                      '.vedmg-remove-featured-btn[data-session-id="' + sessionId + '"]')
                        .prop('disabled', false)
                        .each(function() {
                            var $btn = $(this);
                            if ($btn.hasClass('vedmg-unfeature-session-btn')) {
                                $btn.text('Remove Featured');
                            } else {
                                $btn.text('Remove Featured');
                            }
                        });
                }
            });
        },
        
        /**
         * Show notice message
         */
        showNotice: function(message, type) {
            type = type || 'info';
            
            // Remove existing notices
            $('.vedmg-notice').remove();
            
            // Create notice element
            var noticeClass = 'vedmg-notice notice notice-' + type;
            var notice = $('<div class="' + noticeClass + '" style="margin: 15px 0;"><p>' + message + '</p></div>');
            
            // Insert at the top of the page
            $('.vedmg-classroom-admin').prepend(notice);
            
            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(function() {
                    notice.fadeOut(500, function() {
                        notice.remove();
                    });
                }, 5000);
            }
        },
        
        /**
         * Apply filters to session table
         */
        applyFilters: function() {
            var courseFilter = $('#session-course-filter').val();
            var statusFilter = $('#session-status-filter').val();
            var dateFilter = $('#session-date-filter').val();
            
            $('.vedmg-classroom-table tbody tr').each(function() {
                var $row = $(this);
                var courseText = $row.find('td:nth-child(3)').text().trim();
                var status = $row.find('.vedmg-session-status').attr('data-status');
                var dateText = $row.find('td:nth-child(5)').text().trim();
                
                var showRow = true;
                
                // Apply course filter
                if (courseFilter && !courseText.toLowerCase().includes(courseFilter.toLowerCase())) {
                    showRow = false;
                }
                
                // Apply status filter
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // Apply date filter (basic comparison)
                if (dateFilter && !dateText.includes(dateFilter)) {
                    showRow = false;
                }
                
                // Show/hide row
                if (showRow) {
                    $row.show();
                } else {
                    $row.hide();
                }
            });
            
            // Update counts after filtering
            VedMGSessions.updateSessionCounts();
        },
        
        /**
         * Clear all filters
         */
        clearFilters: function() {
            $('#session-course-filter, #session-status-filter').val('');
            $('#session-date-filter').val('');
            $('.vedmg-classroom-table tbody tr').show();
            VedMGSessions.updateSessionCounts();
        },
        
        /**
         * Perform bulk action on selected sessions
         */
        performBulkAction: function(action, sessionIds) {
            // Show loading state
            $('#apply-session-bulk-action').prop('disabled', true);
            
            // Simulate bulk action
            setTimeout(function() {
                var message = '';
                
                switch(action) {
                    case 'cancel':
                        message = 'Cancelled ' + sessionIds.length + ' sessions successfully!';
                        break;
                    case 'reschedule':
                        message = 'Rescheduled ' + sessionIds.length + ' sessions successfully!';
                        break;
                    case 'delete':
                        message = 'Deleted ' + sessionIds.length + ' sessions successfully!';
                        break;
                    default:
                        message = 'Bulk action completed on ' + sessionIds.length + ' sessions.';
                }
                
                VedMGClassRoomAdmin.showMessage(message, 'success');
                
                // Clear selections
                $('.session-checkbox').prop('checked', false);
                $('#select-all-sessions').prop('checked', false);
                
                // Re-enable bulk action button
                $('#apply-session-bulk-action').prop('disabled', false);
                
                // Update counts
                VedMGSessions.updateSessionCounts();
                
            }, 2000);
        },
        
        /**
         * Handle time change for automatic end time calculation
         */
        handleTimeChange: function() {
            var startTime = $('#schedule-session-start-time').val();
            var duration = parseInt($('#schedule-session-duration').val());
            
            if (startTime && duration) {
                var endTime = VedMGSessions.calculateEndTime(startTime, duration);
                $('#schedule-session-end-time').val(endTime);
            }
        },
        
        /**
         * Handle pagination for sessions (Server-side)
         */
        handlePagination: function(e) {
            // For server-side pagination, let the browser handle the navigation
            // The PHP pagination links will handle the page changes
            console.log('Server-side pagination link clicked');
        },
        
        /**
         * Handle page size change for sessions (Server-side)
         */
        handlePageSizeChange: function() {
            var pageSize = $(this).val();
            console.log('Page size changed to:', pageSize);
            
            // Redirect to the same page with new page size parameter
            var currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('per_page', pageSize);
            currentUrl.searchParams.set('paged', 1); // Reset to first page
            
            window.location.href = currentUrl.toString();
        },
        
        /**
         * Go to specific page
         */
        goToPage: function(page) {
            // Update pagination state
            $('.vedmg-pagination-btn').removeClass('active');
            $('[data-page="' + page + '"]').addClass('active');
            
            // Update pagination info
            var pageSize = parseInt($('#session-pagination-size-select').val());
            var totalItems = parseInt($('#session-pagination-total').text());
            var start = ((page - 1) * pageSize) + 1;
            var end = Math.min(page * pageSize, totalItems);
            
            $('#session-pagination-start').text(start);
            $('#session-pagination-end').text(end);
            
            // Update pagination buttons state
            var totalPages = Math.ceil(totalItems / pageSize);
            $('#session-pagination-first, #session-pagination-prev').prop('disabled', page === 1);
            $('#session-pagination-next, #session-pagination-last').prop('disabled', page === totalPages);
            
            // Show/hide rows based on current page
            VedMGSessions.showRowsForPage(page, pageSize);
            
            console.log('Going to sessions page:', page, 'showing items', start, 'to', end);
        },
        
        /**
         * Show rows for specific page
         */
        showRowsForPage: function(page, pageSize) {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var startIndex = (page - 1) * pageSize;
            var endIndex = startIndex + pageSize;
            
            // First hide all visible rows
            $visibleRows.hide();
            
            // Then show only the rows for current page
            $visibleRows.slice(startIndex, endIndex).show();
            
            console.log('Showing session rows', startIndex, 'to', endIndex - 1, 'of', $visibleRows.length, 'visible rows');
        },
        
        /**
         * Update pagination display
         */
        updatePagination: function() {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var totalItems = $visibleRows.length;
            var pageSize = parseInt($('#session-pagination-size-select').val()) || 10;
            var totalPages = Math.ceil(totalItems / pageSize);
            var currentPage = 1; // Reset to first page
            
            console.log('Updating sessions pagination:', {
                totalItems: totalItems,
                pageSize: pageSize,
                totalPages: totalPages
            });
            
            // Update total count
            $('#session-pagination-total').text(totalItems);
            
            // Update pagination numbers
            var $paginationNumbers = $('.vedmg-pagination-numbers');
            $paginationNumbers.empty();
            
            // Only show pagination if we have more than one page
            if (totalPages > 1) {
                for (var i = 1; i <= Math.min(totalPages, 5); i++) {
                    var activeClass = i === currentPage ? ' active' : '';
                    $paginationNumbers.append('<button class="vedmg-pagination-btn' + activeClass + '" data-page="' + i + '">' + i + '</button>');
                }
                
                // Enable/disable navigation buttons
                $('#session-pagination-first, #session-pagination-prev').prop('disabled', currentPage === 1);
                $('#session-pagination-next, #session-pagination-last').prop('disabled', currentPage >= totalPages);
            } else {
                // Single page or no pages - disable all navigation
                $('#session-pagination-first, #session-pagination-prev, #session-pagination-next, #session-pagination-last').prop('disabled', true);
            }
            
            // Show first page
            VedMGSessions.goToPage(currentPage);
        },
        
        /**
         * Go to next page
         */
        goToNextPage: function() {
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page'));
            this.goToPage(currentPage + 1);
        },
        
        /**
         * Go to previous page
         */
        goToPreviousPage: function() {
            var currentPage = parseInt($('.vedmg-pagination-btn.active').data('page'));
            this.goToPage(currentPage - 1);
        },
        
        /**
         * Go to last page
         */
        goToLastPage: function() {
            var pageSize = parseInt($('#session-pagination-size-select').val());
            var totalItems = parseInt($('#session-pagination-total').text());
            var lastPage = Math.ceil(totalItems / pageSize);
            this.goToPage(lastPage);
        },
        
        /**
         * Set page size
         */
        setPageSize: function(pageSize) {
            // Reset to page 1 when changing page size
            this.goToPage(1);
            console.log('Sessions page size changed to:', pageSize);
        },
        
        /**
         * Update session counts
         */
        updateSessionCounts: function() {
            var $visibleRows = $('.vedmg-classroom-table tbody tr:visible');
            var scheduledCount = 0;
            var completedCount = 0;
            var googleClassroomCount = 0;
            
            $visibleRows.each(function() {
                var status = $(this).find('.vedmg-session-status').attr('data-status');
                if (status === 'scheduled') {
                    scheduledCount++;
                } else if (status === 'completed') {
                    completedCount++;
                } else if (status === 'google classroom') {
                    googleClassroomCount++;
                }
            });
            
            $('#scheduled-count').text(scheduledCount + googleClassroomCount); // Include Google Classroom in scheduled count
            $('#completed-count').text(completedCount);
            
            // Update statistics section
            $('.vedmg-session-stats .vedmg-stat-item').eq(0).find('.vedmg-stat-value').text($visibleRows.length);
            $('.vedmg-session-stats .vedmg-stat-item').eq(2).find('.vedmg-stat-value').text(completedCount);
        },
        
        /**
         * Utility function to get time until a future date
         */
        getTimeUntil: function(futureDate) {
            var now = new Date();
            var diff = futureDate - now;

            if (diff <= 0) return 'now';

            var days = Math.floor(diff / (1000 * 60 * 60 * 24));
            var hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

            if (days > 0) return days + ' day' + (days > 1 ? 's' : '');
            if (hours > 0) return hours + ' hour' + (hours > 1 ? 's' : '');
            return minutes + ' minute' + (minutes > 1 ? 's' : '');
        },

        /**
         * Utility function to get time since a past date
         */
        getTimeSince: function(pastDate) {
            var now = new Date();
            var diff = now - pastDate;

            if (diff <= 0) return 'now';

            var days = Math.floor(diff / (1000 * 60 * 60 * 24));
            var hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

            if (days > 0) return days + ' day' + (days > 1 ? 's' : '');
            if (hours > 0) return hours + ' hour' + (hours > 1 ? 's' : '');
            return minutes + ' minute' + (minutes > 1 ? 's' : '');
        },

        /**
         * Format date for display
         */
        formatDate: function(dateStr) {
            var date = new Date(dateStr);
            return date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        },

        /**
         * Format time for display
         */
        formatTime: function(timeStr) {
            var time = new Date('2000-01-01 ' + timeStr);
            return time.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        },

        /**
         * Format datetime for display
         */
        formatDateTime: function(datetimeStr) {
            var date = new Date(datetimeStr);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        },

        /**
         * Initialize auto-refresh for session statuses
         */
        initAutoRefresh: function() {
            console.log('🔄 Initializing auto-refresh for session statuses');
            
            // Auto-refresh every 2 minutes (120000 ms)
            setInterval(function() {
                VedMGSessions.refreshSessionStatuses();
            }, 120000);
            
            // Also refresh on page focus (when user comes back to tab)
            $(window).on('focus', function() {
                console.log('🔍 Page focused - refreshing session statuses');
                VedMGSessions.refreshSessionStatuses();
            });
            
            console.log('✅ Auto-refresh initialized - will update every 2 minutes');
        },
        
        /**
         * Refresh session statuses from server
         */
        refreshSessionStatuses: function() {
            console.log('🔄 Refreshing session statuses...');
            
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'auto_update_session_statuses',
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    console.log('📥 Session status refresh response:', response);
                    
                    if (response.success) {
                        if (response.data.updated_count > 0) {
                            console.log('✅ Updated ' + response.data.updated_count + ' session statuses');
                            
                            // Show a subtle notification
                            if (typeof VedMGClassRoomAdmin !== 'undefined' && VedMGClassRoomAdmin.showMessage) {
                                VedMGClassRoomAdmin.showMessage(
                                    response.data.updated_count + ' session(s) updated. Refreshing view...', 
                                    'info'
                                );
                            }
                            
                            // Reload the page after a short delay to show updated statuses and ordering
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {
                            console.log('ℹ️ No session status updates needed');
                        }
                    } else {
                        console.log('❌ Failed to refresh session statuses:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('💥 Error refreshing session statuses:', error);
                }
            });
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        VedMGSessions.init();
    });
    
    // Make sessions object available globally
    window.VedMGSessions = VedMGSessions;
    
})(jQuery);
