<?php
/**
 * VedMG ClassRoom Database Upgrade Script
 * 
 * This script handles database schema updates for existing installations.
 * Run this after updating the plugin to add new required columns.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom Database Upgrade Class
 * 
 * Handles database schema upgrades for existing installations
 */
class VedMG_ClassRoom_Database_Upgrade {
    
    /**
     * Current database version
     */
    const CURRENT_DB_VERSION = '2.1';
    
    /**
     * Check and perform database upgrades if needed
     */
    public static function check_and_upgrade() {
        $current_version = get_option('vedmg_classroom_db_version', '1.0');
        
        if (version_compare($current_version, self::CURRENT_DB_VERSION, '<')) {
            self::perform_upgrades($current_version);
        }
    }
    
    /**
     * Perform database upgrades based on current version
     */
    private static function perform_upgrades($from_version) {
        vedmg_log_info('DATABASE', 'Starting database upgrade from version ' . $from_version);
        
        try {
            // Upgrade from version 1.0 to 1.1
            if (version_compare($from_version, '1.1', '<')) {
                self::upgrade_to_1_1();
            }
            
            // Upgrade from version 1.1 to 1.2
            if (version_compare($from_version, '1.2', '<')) {
                self::upgrade_to_1_2();
            }
            
            // Upgrade from version 1.2 to 1.3
            if (version_compare($from_version, '1.3', '<')) {
                self::upgrade_to_1_3();
            }
            
            // Upgrade from version 1.3 to 1.4
            if (version_compare($from_version, '1.4', '<')) {
                self::upgrade_to_1_4();
            }
            
            // Upgrade from version 1.4 to 1.5
            if (version_compare($from_version, '1.5', '<')) {
                self::upgrade_to_1_5();
            }

            // Upgrade from version 1.5 to 1.6
            if (version_compare($from_version, '1.6', '<')) {
                self::upgrade_to_1_6();
            }

            // Upgrade from version 1.6 to 2.0 (Enrollments Enhancement)
            if (version_compare($from_version, '2.0', '<')) {
                self::upgrade_to_2_0();
            }

            // Upgrade from version 2.0 to 2.1 (Sessions Featured Columns)
            if (version_compare($from_version, '2.1', '<')) {
                self::upgrade_to_2_1();
            }

            // Update database version
            update_option('vedmg_classroom_db_version', self::CURRENT_DB_VERSION);
            update_option('vedmg_classroom_db_updated_date', current_time('mysql'));
            
            vedmg_log_info('DATABASE', 'Database upgrade completed successfully to version ' . self::CURRENT_DB_VERSION);
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Database upgrade failed', $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Upgrade database to version 1.1
     * Adds instructor_name and class_join_link columns to courses table
     */
    private static function upgrade_to_1_1() {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
        if (!$table_exists) {
            throw new Exception('Courses table does not exist. Please activate the plugin first.');
        }
        
        // Add instructor_name column if it doesn't exist
        $instructor_name_exists = $wpdb->get_results("SHOW COLUMNS FROM $courses_table LIKE 'instructor_name'");
        if (empty($instructor_name_exists)) {
            $wpdb->query("ALTER TABLE $courses_table ADD COLUMN instructor_name VARCHAR(255) DEFAULT NULL AFTER instructor_id");
            vedmg_log_database('ALTER', 'vedmg_courses', 'Added instructor_name column');
        }
        
        // Add class_join_link column if it doesn't exist
        $class_join_link_exists = $wpdb->get_results("SHOW COLUMNS FROM $courses_table LIKE 'class_join_link'");
        if (empty($class_join_link_exists)) {
            $wpdb->query("ALTER TABLE $courses_table ADD COLUMN class_join_link TEXT DEFAULT NULL AFTER instructor_name");
            vedmg_log_database('ALTER', 'vedmg_courses', 'Added class_join_link column');
        }
        
        // Populate instructor_name for existing courses
        self::populate_instructor_names();
        
        vedmg_log_info('DATABASE', 'Upgrade to version 1.1 completed');
    }
    
    /**
     * Upgrade database to version 1.2
     * Adds assigned_instructor_id column to class_sessions table
     */
    private static function upgrade_to_1_2() {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
        if (!$table_exists) {
            throw new Exception('Class sessions table does not exist. Please activate the plugin first.');
        }
        
        // Add assigned_instructor_id column if it doesn't exist
        $assigned_instructor_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'assigned_instructor_id'");
        if (empty($assigned_instructor_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN assigned_instructor_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER session_status");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added assigned_instructor_id column');
        }
        
        // Add index for assigned_instructor_id
        $index_exists = $wpdb->get_results("SHOW INDEX FROM $sessions_table WHERE Key_name = 'assigned_instructor_id'");
        if (empty($index_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD KEY assigned_instructor_id (assigned_instructor_id)");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added index for assigned_instructor_id column');
        }
        
        vedmg_log_info('DATABASE', 'Upgrade to version 1.2 completed');
    }
    
    /**
     * Populate instructor names for existing courses
     */
    private static function populate_instructor_names() {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Get courses without instructor names
        $courses = $wpdb->get_results("
            SELECT course_id, instructor_id 
            FROM $courses_table 
            WHERE instructor_name IS NULL AND instructor_id > 0
        ");
        
        foreach ($courses as $course) {
            $instructor_name = self::get_instructor_name($course->instructor_id);
            if ($instructor_name) {
                $wpdb->update(
                    $courses_table,
                    array('instructor_name' => $instructor_name),
                    array('course_id' => $course->course_id),
                    array('%s'),
                    array('%d')
                );
            }
        }
        
        vedmg_log_info('DATABASE', 'Populated instructor names for existing courses');
    }
    
    /**
     * Get instructor name by ID
     */
    private static function get_instructor_name($instructor_id) {
        // Try to get from WordPress users table first
        $user = get_userdata($instructor_id);
        if ($user) {
            return $user->display_name;
        }
        
        // Try to get from MasterStudy tables if available
        global $wpdb;
        
        // Check if MasterStudy instructor table exists
        $instructor_table = $wpdb->prefix . 'stm_lms_instructors';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$instructor_table'");
        
        if ($table_exists) {
            $instructor = $wpdb->get_row($wpdb->prepare("
                SELECT display_name 
                FROM $instructor_table 
                WHERE instructor_id = %d
            ", $instructor_id));
            
            if ($instructor) {
                return $instructor->display_name;
            }
        }
        
        // Fallback: return "Unknown Instructor"
        return 'Unknown Instructor';
    }
    
    /**
     * Upgrade database to version 1.3
     * Creates student classroom mappings table for Google Classroom enrollment tracking
     */
    private static function upgrade_to_1_3() {
        global $wpdb;
        
        $student_classroom_mappings_table = $wpdb->prefix . 'vedmg_student_classroom_mappings';
        
        // Check if table already exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$student_classroom_mappings_table'");
        
        if (!$table_exists) {
            $charset_collate = $wpdb->get_charset_collate();
            
            $sql = "CREATE TABLE $student_classroom_mappings_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                student_email VARCHAR(255) NOT NULL,
                google_classroom_id VARCHAR(255) NOT NULL,
                course_id INT NOT NULL,
                enrollment_status ENUM('enrolled', 'pending', 'removed') DEFAULT 'enrolled',
                fetched_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_student_id (student_id),
                INDEX idx_student_email (student_email),
                INDEX idx_google_classroom_id (google_classroom_id),
                INDEX idx_course_id (course_id)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            vedmg_log_database('CREATE', 'vedmg_student_classroom_mappings', 'Created student classroom mappings table');
        }
        
        // Add recurring schedule columns to sessions table if not exists
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
        
        if ($sessions_exists) {
            // Add is_recurring column
            $is_recurring_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'is_recurring'");
            if (empty($is_recurring_exists)) {
                $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE AFTER session_status");
                vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added is_recurring column');
            }
            
            // Add recurrence_pattern column
            $recurrence_pattern_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'recurrence_pattern'");
            if (empty($recurrence_pattern_exists)) {
                $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN recurrence_pattern JSON NULL AFTER is_recurring");
                vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added recurrence_pattern column');
            }
            
            // Add meeting_type column
            $meeting_type_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'meeting_type'");
            if (empty($meeting_type_exists)) {
                $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN meeting_type ENUM('individual', 'class') DEFAULT 'class' AFTER recurrence_pattern");
                vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added meeting_type column');
            }
            
            // Add target_student_id column
            $target_student_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'target_student_id'");
            if (empty($target_student_exists)) {
                $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN target_student_id INT NULL AFTER meeting_type");
                vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added target_student_id column');
            }
        }
        
        vedmg_log_info('DATABASE', 'Upgrade to version 1.3 completed successfully');
    }
    
    /**
     * Upgrade database to version 1.4
     * Adds new columns for enhanced session scheduling functionality
     */
    private static function upgrade_to_1_4() {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
        if (!$table_exists) {
            throw new Exception('Class sessions table does not exist. Please activate the plugin first.');
        }
        
        // Add session_type column if it doesn't exist
        $session_type_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'session_type'");
        if (empty($session_type_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN session_type ENUM('individual','group','class') DEFAULT 'individual' AFTER session_status");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added session_type column');
        }
        
        // Add duration_minutes column if it doesn't exist
        $duration_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'duration_minutes'");
        if (empty($duration_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN duration_minutes INT(11) DEFAULT 60 AFTER session_type");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added duration_minutes column');
        }
        
        // Add is_recurring column if it doesn't exist
        $is_recurring_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'is_recurring'");
        if (empty($is_recurring_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN is_recurring TINYINT(1) DEFAULT 0 AFTER duration_minutes");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added is_recurring column');
        }
        
        // Add recurring_pattern column if it doesn't exist
        $recurring_pattern_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'recurring_pattern'");
        if (empty($recurring_pattern_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN recurring_pattern VARCHAR(50) DEFAULT NULL AFTER is_recurring");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added recurring_pattern column');
        }
        
        // Add recurring_days column if it doesn't exist
        $recurring_days_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'recurring_days'");
        if (empty($recurring_days_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN recurring_days JSON DEFAULT NULL AFTER recurring_pattern");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added recurring_days column');
        }
        
        // Add recurring_dates column if it doesn't exist
        $recurring_dates_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'recurring_dates'");
        if (empty($recurring_dates_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN recurring_dates JSON DEFAULT NULL AFTER recurring_days");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added recurring_dates column');
        }
        
        // Add recurring_count column if it doesn't exist
        $recurring_count_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'recurring_count'");
        if (empty($recurring_count_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN recurring_count INT(11) DEFAULT 1 AFTER recurring_dates");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added recurring_count column');
        }
        
        // Add recurring_end_date column if it doesn't exist
        $recurring_end_date_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'recurring_end_date'");
        if (empty($recurring_end_date_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN recurring_end_date DATE DEFAULT NULL AFTER recurring_count");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added recurring_end_date column');
        }
        
        // Add selected_student_ids column if it doesn't exist
        $selected_student_ids_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'selected_student_ids'");
        if (empty($selected_student_ids_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN selected_student_ids JSON DEFAULT NULL AFTER recurring_end_date");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added selected_student_ids column');
        }
        
        vedmg_log_info('DATABASE', 'Upgrade to version 1.4 completed successfully');
    }
    
    /**
     * Upgrade database to version 1.5
     * Adds 'google classroom' to session_status enum
     */
    private static function upgrade_to_1_5() {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
        
        // Check if table exists
        $sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
        if (!$sessions_exists) {
            throw new Exception('Class sessions table does not exist. Please activate the plugin first.');
        }
        
        // Modify session_status enum to include 'google classroom'
        $wpdb->query("
            ALTER TABLE $sessions_table 
            MODIFY COLUMN session_status ENUM('scheduled','ongoing','completed','cancelled','google classroom') DEFAULT 'scheduled'
        ");
        vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Updated session_status enum to include google classroom');
        
        vedmg_log_info('DATABASE', 'Upgrade to version 1.5 completed successfully');
    }

    /**
     * Upgrade database to version 1.6
     * Adds meeting_link field to vedmg_courses table
     */
    private static function upgrade_to_1_6() {
        global $wpdb;

        $courses_table = $wpdb->prefix . 'vedmg_courses';

        // Check if table exists
        $courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");
        if (!$courses_exists) {
            throw new Exception('Courses table does not exist. Please activate the plugin first.');
        }

        // Add meeting_link field if it doesn't exist
        $meeting_link_exists = $wpdb->get_results("SHOW COLUMNS FROM $courses_table LIKE 'meeting_link'");
        if (empty($meeting_link_exists)) {
            $wpdb->query("ALTER TABLE $courses_table ADD COLUMN meeting_link text DEFAULT NULL AFTER google_classroom_link");
            vedmg_log_database('ALTER', 'vedmg_courses', 'Added meeting_link column');
        }

        vedmg_log_info('DATABASE', 'Upgrade to version 1.6 completed successfully');
    }

    /**
     * Upgrade database to version 2.0
     * Enrollments page enhancement - removes status column, adds instructor name and scheduling tracking
     */
    private static function upgrade_to_2_0() {
        global $wpdb;

        try {
            vedmg_log_info('DATABASE', 'Starting upgrade to version 2.0 - Enrollments Enhancement');

            // Start transaction
            $wpdb->query('START TRANSACTION');

            // Step 1: Backup existing data
            self::backup_enrollment_data_v2();

            // Step 2: Modify vedmg_student_enrollments table
            self::modify_enrollments_table_v2();

            // Step 3: Create session tracking table
            self::create_session_tracking_table_v2();

            // Step 4: Populate instructor names
            self::populate_instructor_names_v2();

            // Commit transaction
            $wpdb->query('COMMIT');

            vedmg_log_info('DATABASE', 'Upgrade to version 2.0 completed successfully');

        } catch (Exception $e) {
            // Rollback on error
            $wpdb->query('ROLLBACK');
            vedmg_log_error('DATABASE', 'Upgrade to version 2.0 failed', $e->getMessage());
            throw $e;
        }
    }

    /**
     * Backup existing enrollment data for v2.0 upgrade
     */
    private static function backup_enrollment_data_v2() {
        global $wpdb;

        $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
        $backup_table = $wpdb->prefix . 'vedmg_student_enrollments_backup_v1';

        // Create backup table
        $sql = "CREATE TABLE IF NOT EXISTS $backup_table AS SELECT * FROM $enrollments_table";
        $result = $wpdb->query($sql);

        if ($result === false) {
            throw new Exception('Failed to create backup table: ' . $wpdb->last_error);
        }

        vedmg_log_info('DATABASE', 'Enrollment data backed up to ' . $backup_table);
    }

    /**
     * Modify the enrollments table structure for v2.0
     */
    private static function modify_enrollments_table_v2() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vedmg_student_enrollments';

        // Check if enrollment_status column exists and remove it
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'enrollment_status'");

        if (!empty($columns)) {
            $sql = "ALTER TABLE $table_name DROP COLUMN enrollment_status";
            $result = $wpdb->query($sql);

            if ($result === false) {
                throw new Exception('Failed to drop enrollment_status column: ' . $wpdb->last_error);
            }

            vedmg_log_info('DATABASE', 'Removed enrollment_status column from enrollments table');
        }

        // Add instructor_name column if it doesn't exist
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'instructor_name'");

        if (empty($columns)) {
            $sql = "ALTER TABLE $table_name ADD COLUMN instructor_name varchar(255) DEFAULT NULL AFTER student_phone";
            $result = $wpdb->query($sql);

            if ($result === false) {
                throw new Exception('Failed to add instructor_name column: ' . $wpdb->last_error);
            }

            vedmg_log_info('DATABASE', 'Added instructor_name column to enrollments table');
        }

        // Add scheduling tracking columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'last_scheduled_date'");

        if (empty($columns)) {
            $sql = "ALTER TABLE $table_name
                    ADD COLUMN last_scheduled_date datetime DEFAULT NULL AFTER completion_date,
                    ADD COLUMN total_sessions_scheduled int(11) DEFAULT 0 AFTER last_scheduled_date,
                    ADD COLUMN last_session_type enum('individual','group','class_wide') DEFAULT NULL AFTER total_sessions_scheduled";
            $result = $wpdb->query($sql);

            if ($result === false) {
                throw new Exception('Failed to add scheduling tracking columns: ' . $wpdb->last_error);
            }

            vedmg_log_info('DATABASE', 'Added scheduling tracking columns to enrollments table');
        }
    }

    /**
     * Create session tracking table for detailed session management
     */
    private static function create_session_tracking_table_v2() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vedmg_session_tracking';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            tracking_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            enrollment_id bigint(20) unsigned NOT NULL,
            student_id bigint(20) unsigned NOT NULL,
            course_id bigint(20) unsigned NOT NULL,
            session_id bigint(20) unsigned DEFAULT NULL,
            session_type enum('individual','group','class_wide') NOT NULL,
            session_status enum('scheduled','completed','cancelled','rescheduled') DEFAULT 'scheduled',
            scheduled_date datetime NOT NULL,
            completed_date datetime DEFAULT NULL,
            session_details text DEFAULT NULL,
            google_event_id varchar(255) DEFAULT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (tracking_id),
            KEY idx_enrollment_id (enrollment_id),
            KEY idx_student_id (student_id),
            KEY idx_course_id (course_id),
            KEY idx_session_status (session_status),
            KEY idx_scheduled_date (scheduled_date)
        ) $charset_collate";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        vedmg_log_info('DATABASE', 'Created session tracking table');
    }

    /**
     * Populate instructor names from course data for v2.0
     */
    private static function populate_instructor_names_v2() {
        global $wpdb;

        $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
        $courses_table = $wpdb->prefix . 'vedmg_courses';

        // Update instructor names from course data
        $sql = "UPDATE $enrollments_table e
                INNER JOIN $courses_table c ON e.course_id = c.course_id
                SET e.instructor_name = c.instructor_name
                WHERE e.instructor_name IS NULL AND c.instructor_name IS NOT NULL";

        $result = $wpdb->query($sql);

        if ($result === false) {
            throw new Exception('Failed to populate instructor names: ' . $wpdb->last_error);
        }

        vedmg_log_info('DATABASE', "Populated instructor names for $result enrollment records");
    }

    /**
     * Upgrade database to version 2.1
     * Adds missing is_featured and featured_date columns to class_sessions table
     */
    private static function upgrade_to_2_1() {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
        if (!$table_exists) {
            throw new Exception('Class sessions table does not exist. Please activate the plugin first.');
        }

        // Add is_featured column if it doesn't exist
        $is_featured_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'is_featured'");
        if (empty($is_featured_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN is_featured TINYINT(1) DEFAULT 0 AFTER updated_date");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added is_featured column');
        }

        // Add featured_date column if it doesn't exist
        $featured_date_exists = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table LIKE 'featured_date'");
        if (empty($featured_date_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD COLUMN featured_date DATETIME DEFAULT NULL AFTER is_featured");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added featured_date column');
        }

        // Add index for is_featured column
        $index_exists = $wpdb->get_results("SHOW INDEX FROM $sessions_table WHERE Key_name = 'is_featured'");
        if (empty($index_exists)) {
            $wpdb->query("ALTER TABLE $sessions_table ADD KEY is_featured (is_featured)");
            vedmg_log_database('ALTER', 'vedmg_class_sessions', 'Added index for is_featured column');
        }

        vedmg_log_info('DATABASE', 'Upgrade to version 2.1 completed successfully');
    }

    /**
     * Run upgrade check (call this from plugin initialization)
     */
    public static function init() {
        // Run upgrade check on admin pages only
        if (is_admin()) {
            add_action('admin_init', array(__CLASS__, 'check_and_upgrade'));
        }
    }
}

// Initialize the upgrade system
VedMG_ClassRoom_Database_Upgrade::init();
