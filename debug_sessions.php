<?php
/**
 * Debug script for VedMG ClassRoom Sessions
 * 
 * This script helps diagnose session display issues by checking:
 * 1. Database table existence
 * 2. Column structure
 * 3. Data presence
 * 4. Query execution
 * 
 * Run this script by accessing it directly in the browser:
 * http://yoursite.com/wp-content/plugins/VedMG-ClassRoom/debug_sessions.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this debug script.');
}

// Include required files
require_once('database/helper.php');

echo "<h1>VedMG ClassRoom Sessions Debug Report</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .info { background-color: #d1ecf1; border-color: #b8daff; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

global $wpdb;

// 1. Check table existence
echo "<div class='section info'>";
echo "<h2>1. Database Table Check</h2>";

$sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
$courses_table = $wpdb->prefix . 'vedmg_courses';

$sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
$courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");

echo "<p><strong>Sessions Table ($sessions_table):</strong> " . ($sessions_exists ? "✅ EXISTS" : "❌ MISSING") . "</p>";
echo "<p><strong>Courses Table ($courses_table):</strong> " . ($courses_exists ? "✅ EXISTS" : "❌ MISSING") . "</p>";
echo "</div>";

if (!$sessions_exists) {
    echo "<div class='section error'>";
    echo "<h2>❌ Critical Error</h2>";
    echo "<p>The sessions table does not exist. Please activate the plugin to create the required tables.</p>";
    echo "</div>";
    exit;
}

// 2. Check table structure
echo "<div class='section info'>";
echo "<h2>2. Table Structure Check</h2>";

$columns = $wpdb->get_results("SHOW COLUMNS FROM $sessions_table");
echo "<h3>Current Columns in $sessions_table:</h3>";
echo "<table>";
echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "</tr>";
}
echo "</table>";

// Check for required columns
$required_columns = ['assigned_instructor_id', 'is_featured', 'featured_date'];
$missing_columns = [];
$existing_column_names = array_column($columns, 'Field');

foreach ($required_columns as $req_col) {
    if (!in_array($req_col, $existing_column_names)) {
        $missing_columns[] = $req_col;
    }
}

if (!empty($missing_columns)) {
    echo "<div class='warning'>";
    echo "<h3>⚠️ Missing Required Columns:</h3>";
    echo "<ul>";
    foreach ($missing_columns as $col) {
        echo "<li>$col</li>";
    }
    echo "</ul>";
    echo "<p>These columns will be added automatically when you access the sessions page.</p>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<h3>✅ All required columns are present</h3>";
    echo "</div>";
}
echo "</div>";

// 3. Check data presence
echo "<div class='section info'>";
echo "<h2>3. Data Presence Check</h2>";

$total_sessions = $wpdb->get_var("SELECT COUNT(*) FROM $sessions_table");
echo "<p><strong>Total Sessions in Database:</strong> $total_sessions</p>";

if ($total_sessions > 0) {
    echo "<h3>Sample Session Data:</h3>";
    $sample_sessions = $wpdb->get_results("SELECT * FROM $sessions_table LIMIT 5");
    echo "<table>";
    echo "<tr>";
    foreach ($sample_sessions[0] as $key => $value) {
        echo "<th>$key</th>";
    }
    echo "</tr>";
    foreach ($sample_sessions as $session) {
        echo "<tr>";
        foreach ($session as $value) {
            echo "<td>" . esc_html($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div class='warning'>";
    echo "<p>⚠️ No sessions found in the database. This could be why the admin panel is empty.</p>";
    echo "</div>";
}
echo "</div>";

// 4. Test the actual query used by the sessions page
echo "<div class='section info'>";
echo "<h2>4. Query Execution Test</h2>";

try {
    echo "<p>Testing the actual query used by the sessions page...</p>";
    
    // Force column check
    VedMG_ClassRoom_Database_Helper::get_class_sessions_with_auto_update(1, 10);
    
    $session_data = VedMG_ClassRoom_Database_Helper::get_class_sessions(1, 10);
    
    echo "<div class='success'>";
    echo "<h3>✅ Query executed successfully</h3>";
    echo "<p><strong>Sessions returned:</strong> " . count($session_data['sessions']) . "</p>";
    echo "<p><strong>Total count:</strong> " . $session_data['total_count'] . "</p>";
    echo "<p><strong>Total pages:</strong> " . $session_data['total_pages'] . "</p>";
    echo "</div>";
    
    if (!empty($session_data['sessions'])) {
        echo "<h3>First Session Data:</h3>";
        echo "<pre>" . print_r($session_data['sessions'][0], true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Query execution failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

// 5. WordPress database error check
echo "<div class='section info'>";
echo "<h2>5. WordPress Database Status</h2>";
if ($wpdb->last_error) {
    echo "<div class='error'>";
    echo "<p><strong>Last Database Error:</strong> " . $wpdb->last_error . "</p>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<p>✅ No database errors detected</p>";
    echo "</div>";
}
echo "</div>";

// 6. Plugin version and upgrade status
echo "<div class='section info'>";
echo "<h2>6. Plugin Version & Upgrade Status</h2>";
$db_version = get_option('vedmg_classroom_db_version', 'Not set');
echo "<p><strong>Database Version:</strong> $db_version</p>";
echo "<p><strong>Expected Version:</strong> 2.0</p>";

if (version_compare($db_version, '2.0', '<')) {
    echo "<div class='warning'>";
    echo "<p>⚠️ Database needs upgrade. This will happen automatically on next page load.</p>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<p>✅ Database is up to date</p>";
    echo "</div>";
}
echo "</div>";

echo "<div class='section info'>";
echo "<h2>7. Recommendations</h2>";
echo "<ul>";
if (empty($missing_columns)) {
    echo "<li>✅ Table structure is correct</li>";
} else {
    echo "<li>⚠️ Missing columns will be added automatically</li>";
}

if ($total_sessions > 0) {
    echo "<li>✅ Session data exists in database</li>";
} else {
    echo "<li>❌ No session data found - create some sessions first</li>";
}

echo "<li>🔄 Try refreshing the admin sessions page after running this debug script</li>";
echo "<li>📝 Check the debug.log file for any error messages</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
