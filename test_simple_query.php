<?php
/**
 * Simple test to check if the database query works
 */

// WordPress environment setup
require_once('C:/xampp/htdocs/paylearn/wp-config.php');

global $wpdb;

echo "=== Simple Sessions Query Test ===\n\n";

$sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
$courses_table = $wpdb->prefix . 'vedmg_courses';

// Check if both tables exist
$sessions_exists = $wpdb->get_var("SHOW TABLES LIKE '$sessions_table'");
$courses_exists = $wpdb->get_var("SHOW TABLES LIKE '$courses_table'");

echo "Sessions table exists: " . ($sessions_exists ? 'YES' : 'NO') . "\n";
echo "Courses table exists: " . ($courses_exists ? 'YES' : 'NO') . "\n\n";

if (!$sessions_exists) {
    echo "❌ Sessions table missing\n";
    exit;
}

if (!$courses_exists) {
    echo "❌ Courses table missing - this might be the issue!\n";
    
    // Let's see what tables do exist
    echo "\nExisting tables with 'vedmg' prefix:\n";
    $tables = $wpdb->get_results("SHOW TABLES LIKE '{$wpdb->prefix}vedmg%'");
    foreach ($tables as $table) {
        $table_name = array_values((array)$table)[0];
        echo "- $table_name\n";
    }
    exit;
}

// Test simple query on sessions table
echo "=== Testing Simple Sessions Query ===\n";
$simple_sessions = $wpdb->get_results("SELECT session_id, session_title, course_id FROM $sessions_table LIMIT 5");
echo "Simple sessions query returned: " . count($simple_sessions) . " results\n";

// Test simple query on courses table  
echo "\n=== Testing Simple Courses Query ===\n";
$simple_courses = $wpdb->get_results("SELECT course_id, course_name FROM $courses_table LIMIT 5");
echo "Simple courses query returned: " . count($simple_courses) . " results\n";

// Test JOIN query
echo "\n=== Testing JOIN Query ===\n";
$join_query = "
    SELECT 
        s.session_id,
        s.session_title,
        s.course_id,
        c.course_name
    FROM $sessions_table s
    LEFT JOIN $courses_table c ON s.course_id = c.course_id
    LIMIT 5
";

$join_results = $wpdb->get_results($join_query);
echo "JOIN query returned: " . count($join_results) . " results\n";

if ($wpdb->last_error) {
    echo "❌ SQL Error: " . $wpdb->last_error . "\n";
} else {
    echo "✅ JOIN query successful\n";
    
    if (!empty($join_results)) {
        echo "\nSample result:\n";
        $sample = $join_results[0];
        echo "Session ID: {$sample->session_id}\n";
        echo "Session Title: {$sample->session_title}\n";
        echo "Course ID: {$sample->course_id}\n";
        echo "Course Name: " . ($sample->course_name ?: 'NULL') . "\n";
    }
}

echo "\nDone.\n";
?>
